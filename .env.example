SERVICE_NAME=elastic-price
APP_ENV=production

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Logging Settings
LOG_LEVEL=DEBUG
LOG_DIR=logs

#Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=pppppppppp

# ClickHouse Settings
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_DB_NAME=default
CLICKHOUSE_USERNAME=clickhouse
CLICKHOUSE_PASSWORD=password
CLICKHOUSE_CLUSTER_NAME=cluster_sl
CLICKHOUSE_MASTER_NODES=localhost,localhost,localhost,localhost
CLICKHOUSE_MASTER_NODES_MAP=[[0,1],[2,3]]
CONNECTION_TIMEOUT=3600


# Load configuration from environment variables with fallbacks
DATABASE_MAIN_HOST=localhost
DATABASE_MAIN_PORT=3306
DATABASE_MAIN_DBNAME=main_db
DATABASE_MAIN_USER=uuuuu
DATABASE_MAIN_PASSWORD=ppppp

# Client database configuration
DATABASE_CLIENT_HOST=localhost,localhost
DATABASE_CLIENT_PORT=3306
DATABASE_CLIENT_DBNAME=client_db_
DATABASE_CLIENT_USER=uuuuu
DATABASE_CLIENT_PASSWORD=ppppp

ELASTIC_PRICE_DB_HOST=localhost
ELASTIC_PRICE_DB_PORT=5432
ELASTIC_PRICE_DB_DATABASE=reports
ELASTIC_PRICE_DB_USERNAME=uuuuu
ELASTIC_PRICE_DB_PASSWORD=ppppp

# Connection pool settings
MAX_DB_CONNECTIONS=20
CONNECTION_TIMEOUT=3600  # 1 hour in seconds
