#!/usr/bin/env python3
"""Tests for JSON encoding and decoding functionality."""

import pytest
import uuid
from datetime import datetime, date, timezone
from decimal import Decimal
import base64
import json

from lib.json import json_encode, json_decode, J<PERSON>Encoder, JsonDecoder


class TestJsonEncoder:
    """Tests for the JsonEncoder class and json_encode function."""
    
    def test_encode_basic_types(self):
        """Test encoding of basic Python types."""
        # Test dictionary with basic types
        data = {
            "string": "hello",
            "integer": 42,
            "float": 3.14,
            "boolean": True,
            "none": None,
            "list": [1, 2, 3],
            "nested": {"key": "value"}
        }
        
        # Encode and decode to verify
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        assert decoded == data
    
    def test_encode_decimal(self):
        """Test encoding of Decimal objects."""
        data = {"amount": Decimal("123.45")}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        assert decoded["amount"] == 123.45
    
    def test_encode_datetime(self):
        """Test encoding of datetime objects."""
        now = datetime(2023, 1, 15, 12, 30, 45, tzinfo=timezone.utc)
        data = {"timestamp": now}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        assert decoded["timestamp"] == now.isoformat()
    
    def test_encode_date(self):
        """Test encoding of date objects."""
        today = date(2023, 1, 15)
        data = {"date": today}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        assert decoded["date"] == today.isoformat()
    
    def test_encode_uuid(self):
        """Test encoding of UUID objects."""
        id = uuid.UUID("550e8400-e29b-41d4-a716-************")
        data = {"id": id}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        assert decoded["id"] == str(id)
    
    def test_encode_bytes(self):
        """Test encoding of bytes objects."""
        binary_data = b"hello world"
        data = {"binary": binary_data}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        expected = base64.b64encode(binary_data).decode('utf-8')
        assert decoded["binary"] == expected
    
    def test_encode_set(self):
        """Test encoding of set objects."""
        data = {"tags": set(["python", "json", "testing"])}
        
        encoded = json_encode(data)
        decoded = json.loads(encoded)
        
        # Sets are converted to lists, order may vary
        assert sorted(decoded["tags"]) == sorted(["python", "json", "testing"])
    
    def test_encode_complex_nested(self):
        """Test encoding of complex nested structures with mixed types."""
        complex_data = {
            "id": uuid.uuid4(),
            "created_at": datetime.now(),
            "amounts": [Decimal("10.50"), Decimal("20.75")],
            "metadata": {
                "tags": set(["important", "urgent"]),
                "binary_data": b"complex test"
            }
        }
        
        # Should not raise any exceptions
        encoded = json_encode(complex_data)
        assert isinstance(encoded, str)
        
        # Verify it can be decoded as standard JSON
        decoded = json.loads(encoded)
        assert isinstance(decoded, dict)
    
    def test_encode_error_handling(self):
        """Test error handling for non-serializable objects."""
        # Create an object that can't be serialized
        class NonSerializable:
            pass
        
        data = {"obj": NonSerializable()}
        
        # Should raise an exception
        with pytest.raises(TypeError):
            json_encode(data)


class TestJsonDecoder:
    """Tests for the JsonDecoder class and json_decode function."""
    
    def test_decode_basic_types(self):
        """Test decoding of basic JSON types."""
        json_str = '{"string":"hello","integer":42,"float":3.14,"boolean":true,"null":null,"array":[1,2,3]}'
        
        decoded = json_decode(json_str)
        
        assert decoded["string"] == "hello"
        assert decoded["integer"] == 42
        assert isinstance(decoded["float"], Decimal)  # Default behavior converts to Decimal
        assert decoded["boolean"] is True
        assert decoded["null"] is None
        assert decoded["array"] == [1, 2, 3]
    
    def test_decode_decimal(self):
        """Test decoding of numbers as Decimal objects."""
        json_str = '{"amount": 123.45, "integer": 42}'
        
        # With parse_decimals=True (default)
        decoded = json_decode(json_str)
        assert isinstance(decoded["amount"], Decimal)
        assert decoded["amount"] == Decimal("123.45")
        assert isinstance(decoded["integer"], Decimal)
        
        # With parse_decimals=False
        decoded = json_decode(json_str, parse_decimals=False)
        assert isinstance(decoded["amount"], float)
        assert decoded["amount"] == 123.45
    
    def test_decode_datetime(self):
        """Test decoding of ISO format datetime strings."""
        timestamp = "2023-01-15T12:30:45+00:00"
        json_str = f'{{"timestamp": "{timestamp}"}}'
        
        # With parse_dates=True (default)
        decoded = json_decode(json_str)
        assert isinstance(decoded["timestamp"], datetime)
        assert decoded["timestamp"].isoformat() == timestamp
        
        # With parse_dates=False
        decoded = json_decode(json_str, parse_dates=False)
        assert isinstance(decoded["timestamp"], str)
        assert decoded["timestamp"] == timestamp
    
    def test_decode_uuid(self):
        """Test decoding of UUID strings."""
        uuid_str = "550e8400-e29b-41d4-a716-************"
        json_str = f'{{"id": "{uuid_str}"}}'
        
        # With parse_uuids=True (default)
        decoded = json_decode(json_str)
        assert isinstance(decoded["id"], uuid.UUID)
        assert str(decoded["id"]) == uuid_str
        
        # With parse_uuids=False
        decoded = json_decode(json_str, parse_uuids=False)
        assert isinstance(decoded["id"], str)
        assert decoded["id"] == uuid_str
    
    def test_decode_bytes_input(self):
        """Test decoding from bytes input."""
        json_bytes = b'{"message": "hello world"}'
        
        decoded = json_decode(json_bytes)
        
        assert decoded["message"] == "hello world"
    
    def test_decode_none_input(self):
        """Test decoding of None input."""
        result = json_decode(None)
        assert result is None
    
    def test_decode_invalid_json(self):
        """Test decoding of invalid JSON."""
        invalid_json = '{"unclosed": "string"'
        
        result = json_decode(invalid_json)
        assert result is None
    
    def test_decode_complex_nested(self):
        """Test decoding of complex nested structures."""
        # Create a complex JSON string with various types
        uuid_str = "550e8400-e29b-41d4-a716-************"
        timestamp = "2023-01-15T12:30:45+00:00"
        
        json_str = f'''{{
            "id": "{uuid_str}",
            "created_at": "{timestamp}",
            "amounts": [10.50, 20.75],
            "metadata": {{
                "tags": ["important", "urgent"],
                "count": 42
            }}
        }}'''
        
        decoded = json_decode(json_str)
        
        assert isinstance(decoded["id"], uuid.UUID)
        assert isinstance(decoded["created_at"], datetime)
        assert isinstance(decoded["amounts"][0], Decimal)
        assert decoded["metadata"]["tags"] == ["important", "urgent"]
        assert isinstance(decoded["metadata"]["count"], Decimal)


class TestRoundTrip:
    """Tests for round-trip encoding and decoding."""
    
    def test_roundtrip_complex_object(self):
        """Test round-trip encoding and decoding of complex objects."""
        # Create a complex object with various types
        original = {
            "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
            "created_at": datetime(2023, 1, 15, 12, 30, 45, tzinfo=timezone.utc),
            "amounts": [Decimal("10.50"), Decimal("20.75")],
            "metadata": {
                "tags": set(["important", "urgent"]),
                "binary_data": b"test data"
            }
        }
        
        # Encode to JSON
        encoded = json_encode(original)
        
        # Decode back to Python
        decoded = json_decode(encoded)
        
        # Verify key properties
        assert decoded["id"] == original["id"]
        assert decoded["created_at"] == original["created_at"]
        assert decoded["amounts"][0] == original["amounts"][0]
        assert decoded["amounts"][1] == original["amounts"][1]
        assert set(decoded["metadata"]["tags"]) == original["metadata"]["tags"]
        # Binary data is base64 encoded/decoded, so direct comparison won't work
    
    def test_custom_encoder_decoder(self):
        """Test using custom encoder and decoder classes."""
        data = {"value": 42}
        
        # Custom encoder that adds a prefix to all strings
        class CustomEncoder(JsonEncoder):
            def default(self, obj):
                if isinstance(obj, str):
                    return f"PREFIX_{obj}"
                return super().default(obj)
        
        # Custom decoder that removes the prefix
        class CustomDecoder(JsonDecoder):
            def _object_hook(self, obj):
                obj = super()._object_hook(obj)
                for key, value in obj.items():
                    if isinstance(value, str) and value.startswith("PREFIX_"):
                        obj[key] = value[7:]
                return obj
        
        # Use custom classes
        encoded = json_encode(data, cls=CustomEncoder)
        decoded = json_decode(encoded, cls=CustomDecoder)
        
        assert decoded["value"] == data["value"]


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])