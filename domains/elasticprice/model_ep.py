from datetime import datetime, timedelta, timezone
from typing import Generator, Optional
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import TensorDataset
import pandas as pd
import logging

from domains.elasticprice.loader import Loader
from domains.elasticprice.model import ElasticPriceNN, train_model

from domains.models.trained_model import TrainedModel
from domains.repository.bas_repository import BasRepository
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository

from pathlib import Path

logger = logging.getLogger(__name__)


class ElasticPriceModel:
    """Base class for elastic price models."""

    def __init__(self):

        self._last_trained_model = None
        self._torch_model = None
        self._loss = -1
        self._accuracy = -1
        self._training_data_size = 0
        self._validation_data_size = 0
        self._start_time = datetime.now(timezone.utc)
        self._load_data_start_date = datetime.now(timezone.utc)

        self._loader = Loader(ProductRepository.new_instance(), ElasticPriceRepository.new_instance(), BasRepository.new_instance())
        self._product_repo = ProductRepository.new_instance()
        self._repo = ElasticPriceRepository.new_instance()


        self._model_name = ''
        self._dimensions = []
    #def

    def _validate(self):
        if not self._model_name:
            raise ValueError("model_name is not set")
        
        if not self._dimensions:
            raise ValueError("dimensions is not set")
    #def


    def load_model(self, last_trained_model:Optional[TrainedModel]):
        if last_trained_model is not None and last_trained_model.model_dump is not None:
            self._torch_model = ElasticPriceNN.load_model_dump(last_trained_model.model_dump)
        
        if not self._torch_model :
            self._torch_model = ElasticPriceNN.create_new_model(self._dimensions)
    #def

    def train(self, force_new_model:bool=False, snapshot_days:int=100, data_bulk_size:int=100000):
        self._validate()        

        if snapshot_days < 1:
            snapshot_days = 100

        if data_bulk_size < 1:
            data_bulk_size = 100000

        if force_new_model:
            self._last_trained_model = None
        else:
            self._last_trained_model = self._repo.get_last_active_trained_model(self._model_name)
        
        now = datetime.now(timezone.utc)

        if self._last_trained_model :
            history_start_date = self._last_trained_model.start_time
        else:
            history_start_date = now


        self._load_data_start_date = history_start_date - timedelta(days=snapshot_days)
        self._model_new_path = f"torch_model_{self._model_name}_{now.strftime('%Y%m%d%H%M%S')}.pt"

        self.load_model(self._last_trained_model)

        print('START TRAINING')
        print('history_start_date=', history_start_date)
        print('snapshot start_date=', self._load_data_start_date)

        data = []
        for new_data in self._load_data():
            data.extend(new_data)
            data_len = len(data)
            print('len(data)=', data_len)

            if data_len > data_bulk_size:
                self._train_model(pd.DataFrame(data))
                data = []
        # for

        if len(data) > 0:
            self._train_model(pd.DataFrame(data))
            data = []
        
        self._store_new_model()
    #def

    def tarain_from_csv(self, file_path:str, force_new_model:bool=False):
        self._validate()

        if force_new_model:
            self._last_trained_model = None
        else:
            self._last_trained_model = self._repo.get_last_active_trained_model(self._model_name)
        
        self.load_model(self._last_trained_model)

        df = pd.read_csv(file_path)
        self._train_model(df)
        self._store_new_model()
    #def

    def _load_data(self)->Generator[list, None, None]:
        customers =  self._product_repo.get_customer_ids()

        if len(customers) == 0:
            return
        
        for customer in customers:
            print('='*100)
            print('customer_id=', customer['id'])

            for new_data in self._loader.load_customer_data2(int(customer['id']), self._load_data_start_date):
                yield new_data
            # for
        # for
    #def    

    def _build_x_y(self, df:pd.DataFrame)->tuple[list, list] :
        features = []
        predictions = []

        # must be implemented in derived class

        # for index, row in df.iterrows():
        #     if row["item_price"] <= 0:
        #         continue

        #     features.append(build_x_feature(row))        
        #     predictions.append(build_y_from_row(row))
        # #for

        return features, predictions
    #def
    
    def _build_train_data_sets(self, df:pd.DataFrame)->tuple[torch.utils.data.TensorDataset, torch.utils.data.TensorDataset] :
        features, predictions = self._build_x_y(df)
        
        X_train, X_valid, y_train, y_valid = train_test_split(features, predictions, test_size=0.2, random_state=42)

        # Convert data to tensors
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32)
        X_test_tensor = torch.tensor(X_valid, dtype=torch.float32)
        y_test_tensor = torch.tensor(y_valid, dtype=torch.float32)

        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        return train_dataset, test_dataset
    #def

    def _store_new_model(self) -> None :
        if self._torch_model is None:
            return
        

        if self._training_data_size == 0 :
            if self._last_trained_model is not None:
                self._training_data_size = self._last_trained_model.training_data_size
                self._validation_data_size = self._last_trained_model.validation_data_size
                self._loss = self._last_trained_model.loss
                self._accuracy = self._last_trained_model.accuracy
            else:
                self._training_data_size = 0
                self._validation_data_size = 0
                self._loss = 0
                self._accuracy = 0
        # if

        self._torch_model.save_model(self._model_new_path)

        end_time = datetime.now(timezone.utc)
        path = Path(self._model_new_path)
                
        if path.exists() and path.is_file():
            with open(self._model_new_path, "rb") as f:
                model_dump = f.read()

                self._repo.store_trained_model(self._model_name, model_dump, self._loss, self._accuracy, self._training_data_size, self._validation_data_size, self._start_time, end_time)          
            path.unlink()
    #def


    def _train_model(self, df:pd.DataFrame):
        if df is None:
            return
        if df.empty:
            return
        
        # print('train_model')
        # print('df.size=', df.size)
        # print('df.shape=', df.shape)

        train_dataset, test_dataset = self._build_train_data_sets(df)

        self._training_data_size += len(train_dataset)
        self._validation_data_size += len(test_dataset)

        model, loss, accuracy = train_model(self._torch_model, train_dataset, test_dataset, epochs=1000, patience_limit=10)

        self._torch_model = model
        self._loss = loss
        self._accuracy = accuracy

        print('loss=', self._loss)
        print('accuracy=', self._accuracy)
        print('training_data_size=', self._training_data_size)
    #def
#class

