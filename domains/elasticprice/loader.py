#!/usr/bin/env python3

from typing import Any, Dict, List, Optional, Generator
import pandas as pd
from domains.models.product_optimization_snapshot import ProductOptimizationSnapshot
from domains.repository.bas_repository import BasRepository
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository
from domains.elasticprice.model_data import build_optimization_limit_params
from datetime import datetime
from pprint import pprint


class Loader:

    def __init__(self, product_repo: ProductRepository, eprice_repo: ElasticPriceRepository, bas_repo: BasRepository):
        self._product_repo = product_repo
        self._eprice_repo = eprice_repo
        self._bas_repo = bas_repo
    #def


    def load_product_optimization(self, snapshot:ProductOptimizationSnapshot)->tuple[dict, list]:        
        if snapshot.amount == 0:
            return {}, []

        optimizations = snapshot.data
        order_keys = []
        optimization_data = {}

        for row in optimizations:
            product = row['product']
            key = f"{product['asin']}-{product['sku']}-{product['marketplace_id']}"
            
            optimization_data[key] = row

            for s in row['amazon_seller_ids']:
                order_keys.append((product['asin'], product['sku'], product['marketplace_id'], s['seller_id']))
        #for

        return optimization_data, order_keys
    #def

    def load_snapshot_data(self, snapshot:ProductOptimizationSnapshot)->Generator[list, None, None]:
        customer_id = snapshot.customer_id
        history_start_date = snapshot.snapshot_date_start
        history_end_date = snapshot.snapshot_date_end
        # optimization_data = snapshot.data

        # print('customer_id=', customer_id)
        # print('history_start_date=', history_start_date)
        # print('history_end_date=', history_end_date)
        # print('optimization_data=', optimization_data)

        # import sys
        # sys.exit(0)

        optimization_data, order_keys = self.load_product_optimization(snapshot)

        order_limit = 1000000
        order_offset = 0
        order_continue_loading = True
        while order_continue_loading:
            history = self._bas_repo.get_amazon_order_history(customer_id, history_start_date, history_end_date, order_keys, order_limit, order_offset)
            order_offset = order_offset + order_limit
        
            print('history=', len(history))
            # print(history)

            if len(history) < order_limit:
                order_continue_loading = False

            new_data = []
            
            for row in history:
                key = f"{row['asin']}-{row['sku']}-{row['order_marketplace_id']}"
                if key not in optimization_data:
                    continue

                product_optimization_limits =  build_optimization_limit_params(optimization_data[key]['optimization_limits'])

                product_row = {
                    'customer_id': customer_id,
                    'version': 1,
                    'stock': max(optimization_data[key]['product']['stock'], optimization_data[key]['product']['local_stock']),
                    'title': optimization_data[key]['product']['title'],
                }

                row['item_price'] = row['total_price'] / row['total_quantity']

                new_row = {**row, **product_row, **product_optimization_limits}
                

                new_data.append(new_row)

            if len(new_data) > 0:
                yield new_data
    #def

    def load_customer_data2(self, customer_id:int, start_date:datetime)->Generator[list, None, None]:

        snapshots = self._eprice_repo.get_product_optimization_snapshots(customer_id, start_date)

        # # print('customer_id=', customer_id)
        # # print('start_date=', start_date)
        # # print('snapshots=', len(snapshots))
        # print(snapshots)

        # import sys
        # sys.exit(0)

        for snapshot in snapshots:
            print('snapshot=', snapshot)

            if snapshot.amount == 0:
                continue

            yield from self.load_snapshot_data(snapshot)
    #def

    def load_customer_data(self, customer_id:int, history_start_date:datetime, history_end_date:datetime)->Generator[list, None, None]:

        limit = 1000
        offset = 0
        continue_loading = True
        while continue_loading:
            optimization_data, order_keys = self.load_product_optimization(customer_id, limit, offset)
            offset = offset + limit

            if len(order_keys) < limit:
                continue_loading = False

            order_limit = 1000000
            order_offset = 0
            order_continue_loading = True
            while order_continue_loading:
                history = self._bas_repo.get_amazon_order_history(customer_id, history_start_date, history_end_date, order_keys, order_limit, order_offset)
                order_offset = order_offset + order_limit
            
                # print('history=', len(history))
                # print(history)

                if len(history) < limit:
                    order_continue_loading = False

                new_data = []
                
                for row in history:
                    key = f"{row['asin']}-{row['sku']}-{row['order_marketplace_id']}"
                    if key not in optimization_data:
                        continue

                    product_optimization_limits =  build_optimization_limit_params(optimization_data[key]['optimization_limits'])

                    product_row = {
                        'customer_id': customer_id,
                        'version': 1,
                        'stock': max(optimization_data[key]['product']['stock'], optimization_data[key]['product']['local_stock']),
                        'title': optimization_data[key]['product']['title'],
                    }

                    row['item_price'] = row['total_price'] / row['total_quantity']

                    new_row = {**row, **product_row, **product_optimization_limits}
                    

                    new_data.append(new_row)

                if len(new_data) > 0:
                    yield new_data
    #def

    def load_data(self, customer_id: int, history_start_date: datetime, history_end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Load and process all data for a customer in a specific time period.
        
        Args:
            customer_id: The ID of the customer to load data for
            history_start_date: Start date for historical data
            history_end_date: End date for historical data
            
        Returns:
            DataFrame containing all processed customer data, or None if no data is available
        """
        data = []
        
        # Use the generator to collect all data chunks
        for df_chunk in self.load_customer_data(customer_id, history_start_date, history_end_date):
            data.extend(df_chunk)
        
        # If no data was found, return None
        if not data:
            return None
        
        # Combine all chunks into a single DataFrame
        df = pd.DataFrame(data)
                
        return df
    #def

    

#class
