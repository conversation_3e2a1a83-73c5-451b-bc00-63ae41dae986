#!/usr/bin/env python3
"""
Test script for the enhanced price prediction model
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

import torch
import pandas as pd
import numpy as np
from datetime import datetime

from domains.elasticprice.enhanced_price_model import (
    EnhancedPricePredictor,
    SalesDataPreprocessor,
    predict_optimal_price,
    load_enhanced_model
)


def test_model_outputs():
    """Test the enhanced model with sample data"""
    print("=== Testing Enhanced Price Prediction Model ===")
    
    # Create sample data
    sample_data = pd.DataFrame({
        'asin': ['B09TRB27ZP'] * 5,
        'sku': ['test-sku'] * 5,
        'order_marketplace_id': ['A1PA6795UKMFR9'] * 5,
        'total_quantity': [1, 2, 1, 3, 2],
        'total_price': [3895, 7790, 3895, 11685, 7790],  # In cents
        'order_purchase_date': pd.date_range('2024-01-01', periods=5),
        'prev_total_quantity': [1, 1, 2, 1, 3],
        'prev_total_price': [3895, 3895, 7790, 3895, 11685],
        'delta_quantity': [0, 1, -1, 2, -1],
        'delta_price_per_quantity': [0, 0, 0, 0, 0],
        'step_price_type': [1, 1, 0, 1, 0],
        'step_point': [0, 1, 0, 1, 0],
        'step_price': [1.5, 2.0, 1.0, 3.0, 1.5],
        'recommended_steps': [2, 3, 1, 4, 2],
        'price_volatility': [0.1, 0.2, 0.1, 0.3, 0.2],
        'quantity_trend': [0, 1, -1, 2, -1],
        'days_since_last_sale': [1, 1, 1, 1, 1]
    })
    
    print(f"Sample data shape: {sample_data.shape}")
    
    # Initialize preprocessor
    print("\n1. Initializing preprocessor...")
    preprocessor = SalesDataPreprocessor()
    
    try:
        processed_data = preprocessor.fit_transform(sample_data)
        print(f"✓ Preprocessing successful")
        print(f"  Numeric features shape: {processed_data['numeric_features'].shape}")
        print(f"  Vocabulary sizes: {processed_data['vocab_sizes']}")
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        return
    
    # Initialize model
    print("\n2. Initializing model...")
    try:
        model = EnhancedPricePredictor(
            numeric_dim=processed_data['numeric_features'].shape[1],
            vocab_sizes=processed_data['vocab_sizes'],
            embed_dim=16,  # Smaller for testing
            hidden_dim=64,  # Smaller for testing
            num_heads=4,
            dropout_rate=0.1
        )
        print(f"✓ Model initialized successfully")
        print(f"  Parameters: {sum(p.numel() for p in model.parameters()):,}")
    except Exception as e:
        print(f"✗ Model initialization failed: {e}")
        return
    
    # Test forward pass
    print("\n3. Testing forward pass...")
    try:
        model.eval()
        with torch.no_grad():
            # Get a sample batch
            numeric_features = torch.tensor(processed_data['numeric_features'][:1], dtype=torch.float32)
            categorical_features = {
                k: torch.tensor(v[:1], dtype=torch.long) 
                for k, v in processed_data['categorical_features'].items()
            }
            
            predictions = model(numeric_features, categorical_features)
            
            print("✓ Forward pass successful")
            print("  Predictions:")
            for key, value in predictions.items():
                print(f"    {key}: {value}")
                
            # Check for reasonable ranges
            step_price = predictions['step_price'].item()
            recommended_steps = predictions['recommended_steps'].item()
            
            print(f"\n  Validation:")
            print(f"    step_price in range [0.5, 20]: {0.5 <= step_price <= 20}")
            print(f"    recommended_steps in range [1, 5]: {1 <= recommended_steps <= 5}")
            
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return
    
    # Test prediction function
    print("\n4. Testing prediction function...")
    try:
        prediction = predict_optimal_price(
            model=model,
            preprocessor=preprocessor,
            asin='B09TRB27ZP',
            sku='test-sku',
            marketplace_id='A1PA6795UKMFR9',
            current_price=38.95,
            current_quantity=1
        )
        
        print("✓ Prediction function successful")
        print("  Results:")
        for key, value in prediction.items():
            print(f"    {key}: {value}")
            
    except Exception as e:
        print(f"✗ Prediction function failed: {e}")
        return
    
    print("\n=== All tests passed! ===")


def test_existing_model():
    """Test loading and using existing trained model"""
    print("\n=== Testing Existing Trained Model ===")
    
    try:
        model, preprocessor = load_enhanced_model('enhanced_price_model.pt')
        print("✓ Model loaded successfully")
        
        # Test prediction
        prediction = predict_optimal_price(
            model=model,
            preprocessor=preprocessor,
            asin='B09TRB27ZP',
            sku='3001-I-Fliegenfransen-Black-pony',
            marketplace_id='A1PA6795UKMFR9',
            current_price=38.95,
            current_quantity=1
        )
        
        print("✓ Prediction successful")
        print("  Results:")
        for key, value in prediction.items():
            print(f"    {key}: {value}")
            
    except FileNotFoundError:
        print("✗ No trained model found (enhanced_price_model.pt)")
    except Exception as e:
        print(f"✗ Error testing existing model: {e}")


if __name__ == "__main__":
    test_model_outputs()
    test_existing_model()
