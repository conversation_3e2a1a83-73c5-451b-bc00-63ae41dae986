
from pathlib import Path
from typing import Generator, Optional
import numpy as np
import pandas as pd
from sklearn import preprocessing
import torch
from torch.utils.data import TensorDataset
from sklearn.model_selection import train_test_split
from datetime import datetime, timedelta, timezone

from domains.elasticprice.model import ElasticPriceNN, train_model
from domains.elasticprice.model_data import text_to_vec
from domains.models.trained_model import TrainedModel
from domains.repository.bas_repository import BasRepository
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository

def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
        Data columns (total 11 columns):
        #   Column                   Non-Null Count  Dtype  
        ---  ------                   --------------  -----  
        0   order_purchase_date      3658 non-null   object 
        1   asin                     3658 non-null   object 
        2   sku                      3658 non-null   object 
        3   order_marketplace_id     3658 non-null   object 
        4   total_orders             3658 non-null   int64  
        5   total_quantity           3658 non-null   int64  
        6   total_price              3658 non-null   int64  
    """

    

    # Step 2: Sort by product and date
    df = df.sort_values(['customer_id', 'order_marketplace_id', 'asin', 'sku', 'order_purchase_date'])
    df = df.reset_index(drop=True)

    # # Step 3: Calculate previous day price/quantity for each product
    df['order_purchase_date_previous'] = df.groupby(['customer_id', 'order_marketplace_id', 'asin', 'sku'])['order_purchase_date'].shift(1)
    
    df['total_quantity_previous'] = df.groupby(['customer_id', 'order_marketplace_id', 'asin', 'sku'])['total_quantity'].shift(1)
    df['total_price_previous'] = df.groupby(['customer_id', 'order_marketplace_id', 'asin', 'sku'])['total_price'].shift(1)
    df['price_per_quantity_previous'] = df['total_price_previous'] / df['total_quantity_previous']
    

    df = df.dropna(subset=['total_quantity_previous', 'total_price_previous'])
    df = df.reset_index(drop=True)

    # # Step 4: Calculate current price per quantity and differences
    df['price_per_quantity'] = df['total_price'] / df['total_quantity']

    df['delta_quantity'] = df['total_quantity'] - df['total_quantity_previous']
    df['delta_price_per_quantity'] = df['price_per_quantity'] - df['price_per_quantity_previous']

    # Step 5: Define targets

    # 5a. step_price_type: UP(1) if price per quantity increased, else DOWN(0)
    df['step_price_type'] = ((df['delta_quantity'] >= 0) & (df['delta_price_per_quantity'] >= 0)).astype(int)

    # 5b. step_point: PERCENT(1) if percent change > 10%, else VALUE(0)
    df['pct_change'] = df['delta_price_per_quantity'] / df['price_per_quantity_previous'].replace(0, 1e-5)
    df['step_point'] = (df['pct_change'].abs() > 0.1).astype(int)

    # 5c. step_steps: number of steps proportional to quantity change, clipped to 1-5
    df['step_steps'] = ((df['delta_quantity'].abs() / 2)+1).clip(1, 5).round().astype(int)

    # 5d. step_price: magnitude of price step, clipped for stability
    df['step_price'] = df['delta_price_per_quantity'].abs().clip(0.5, 10)


    # df.to_csv(f'processed_data_ep2_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv', index=False)
    # import sys
    # sys.exit(0)


    return df
#def


def build_x_feature(asin: str, sku: str, marketplace_id: str) -> list:
    # 20
    asin_x = text_to_vec(asin, 20)

    # 50
    sku_x = text_to_vec(sku, 50)

    #20
    marketplace_x = text_to_vec(marketplace_id, 20)

    # 90
    feature = marketplace_x + asin_x + sku_x 

    feature_arr = np.array(feature)
    feature_normalized = preprocessing.normalize([feature_arr])
    feature_x  = feature_normalized[0].tolist()

    return feature_x
# def


def build_y(row:pd.Series)->list:
    result = []

    result.append(row['step_price_type'])
    result.append(row['step_point'])
    result.append(row['step_price'])
    result.append(row['step_steps'])

    return result
# def


# --- Prediction function ---
def predict_result(model:ElasticPriceNN, asin:str, sku:str, marketplace:str)->Optional[dict]:
    if not model:
        return None
    
    x_features = build_x_feature(
        asin, 
        sku, 
        marketplace,
    )

    predicted_arr = model.predict(x_features)
    if predicted_arr is None:
        return None

    predicted = predicted_arr[0]

    if len(predicted) != 4:
        return None

    result = {
        "step_price_type": "UP" if predicted[0] == 1 else "DOWN",
        "step_point": "PERCENT" if predicted[1] == 1 else "VALUE",
        "step_price": round(predicted[2], 2),
        "recommended_steps": int(predicted[3]),
    }

    return result
#def


def build_x_y(df:pd.DataFrame)->tuple[list, list] :
    features = []
    predictions = []

    for index, row in df.iterrows():        
        features.append(build_x_feature(row['asin'], row['sku'], row['order_marketplace_id']))
        predictions.append(build_y(row))
    #for

    return features, predictions
#def


def load_model(last_trained_model:Optional[TrainedModel], dimensions:Optional[list[int]] = None)->Optional[ElasticPriceNN]:
    model = None
    if last_trained_model is not None and last_trained_model.model_dump is not None:
        model = ElasticPriceNN.load_model_dump(last_trained_model.model_dump)
    
    if not model and dimensions :
        model = ElasticPriceNN.create_new_model(dimensions)

    return model
#def



class ModelEp2Trainer:

    def __init__(self):
        self._model_name = 'ep2'
        self._dimensions = [90, 64, 48, 32, 4]

        self._last_trained_model = None
        self._torch_model = None
        self._torch_model_was_trained = False
        self._loss = -1
        self._accuracy = -1
        self._training_data_size = 0
        self._validation_data_size = 0
        self._start_time = datetime.now(timezone.utc)
        self._load_data_start_date = datetime.now(timezone.utc)

        self._bas_repo = BasRepository.new_instance()
        self._product_repo = ProductRepository.new_instance()
        self._repo = ElasticPriceRepository.new_instance()

        self._snapshot_days = 3
        self._data_bulk_size = 1000000
        self._epochs = 10000  # Increased epochs for better convergence
        self._patience_limit = 10  # Increased patience for better convergence
        self._model_new_path = f"torch_model_{self._model_name}_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}.pt"
    #def
    

    def train(self, force_new_model:bool=False, snapshot_days:int=3, data_bulk_size:int=1000000):
        if snapshot_days > 1:
            self._snapshot_days = snapshot_days

        if data_bulk_size > 1:
            self._data_bulk_size = data_bulk_size

        if force_new_model:
            self._last_trained_model = None
        else:
            self._last_trained_model = self._repo.get_last_active_trained_model(self._model_name)
        
        now = datetime.now(timezone.utc)

        if self._last_trained_model :
            history_start_date = self._last_trained_model.start_time
        else:
            history_start_date = now

        self._load_data_start_date = history_start_date - timedelta(days=self._snapshot_days)

        self._torch_model = load_model(self._last_trained_model, self._dimensions)

        print('START TRAINING')
        print('history_start_date=', history_start_date)
        print('snapshot start_date=', self._load_data_start_date)

        for data in self.load_data(self._load_data_start_date, datetime.now(timezone.utc), self._data_bulk_size):
            self._train_model(pd.DataFrame(data))
        # for

        self._store_new_model()
    #def

    def load_data(self, start_date:datetime, end_date:datetime, limit:int=1000000, offset:int=0)->Generator[list[dict], None, None]:
        customers =  self._product_repo.get_customer_ids()

        if len(customers) == 0:
            return
        
        data = []
        for customer in customers:
            print('='*100)
            print('customer_id=', customer['id'])

            for new_data in self.load_customer_data(int(customer['id']), start_date, end_date, limit, offset):
                data.extend(new_data)
                data_len = len(data)
                print('len(data)=', data_len)

                if data_len > self._data_bulk_size:
                    yield data
                    data = []
            # for
        # for

        if len(data) > 0:
            yield data
    #def

    def load_customer_data(self, customer_id:int, start_date:datetime, end_date:datetime, limit:int=1000000, offset:int=0)->Generator[list[dict], None, None]:
        continue_loading = True
        load_offset = offset

        while continue_loading:
            history = self._bas_repo.get_all_order_history(customer_id, start_date, end_date, limit, load_offset)
            load_offset = load_offset + limit 

            if len(history) < limit :
                continue_loading = False

            yield history
        # while
    #def


    def train_from_csv(self, file_path:str, force_new_model:bool=True, save_to_file_only:bool=False):
        if force_new_model:
            self._last_trained_model = None
        else:
            self._last_trained_model = self._repo.get_last_active_trained_model(self._model_name)
        
        self._torch_model = load_model(self._last_trained_model, self._dimensions)

        df = pd.read_csv(file_path)
        self._train_model(df)
        self._store_new_model(save_to_file_only)
    #def


    def _build_train_data_sets(self, df:pd.DataFrame)->tuple[torch.utils.data.TensorDataset, torch.utils.data.TensorDataset] :
        features, predictions = build_x_y(df)
            
        X_train, X_valid, y_train, y_valid = train_test_split(features, predictions, test_size=0.2, random_state=42)

        # Convert data to tensors
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32)
        X_test_tensor = torch.tensor(X_valid, dtype=torch.float32)
        y_test_tensor = torch.tensor(y_valid, dtype=torch.float32)

        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        return train_dataset, test_dataset
    #def


    def _train_model(self, df:pd.DataFrame):
        if df is None:
            return
        if df.empty:
            return

        df = preprocess_data(df)

        train_dataset, test_dataset = self._build_train_data_sets(df)

        self._training_data_size += len(train_dataset)
        self._validation_data_size += len(test_dataset)

        # Use better hyperparameters for more stable training
        batch_size = min(32, len(train_dataset) // 10)  # Dynamic batch size, max 32
        batch_size = max(1, batch_size)  # Ensure at least 1
        learning_rate = 0.0001  # Lower learning rate for more stable convergence

        model, loss, accuracy = train_model(
            self._torch_model,
            train_dataset,
            test_dataset,
            epochs=self._epochs,
            patience_limit=self._patience_limit,
            batch_size=batch_size,
            learning_rate=learning_rate
        )

        self._torch_model = model
        self._loss = loss
        self._accuracy = accuracy
        self._torch_model_was_trained = True

        print('loss=', self._loss)
        print('accuracy=', self._accuracy)
        print('training_data_size=', self._training_data_size)
    #def

    def _store_new_model(self, save_to_file_only:bool=False) -> None :
        if self._torch_model is None:
            return
        
        if not self._torch_model_was_trained:
            return
        

        if self._training_data_size == 0 :
            if self._last_trained_model is not None:
                self._training_data_size = self._last_trained_model.training_data_size
                self._validation_data_size = self._last_trained_model.validation_data_size
                self._loss = self._last_trained_model.loss
                self._accuracy = self._last_trained_model.accuracy
            else:
                self._training_data_size = 0
                self._validation_data_size = 0
                self._loss = 0
                self._accuracy = 0
        # if

        self._torch_model.save_model(self._model_new_path)
        if save_to_file_only:
            return

        end_time = datetime.now(timezone.utc)
        path = Path(self._model_new_path)
                
        if path.exists() and path.is_file():
            with open(self._model_new_path, "rb") as f:
                model_dump = f.read()

                self._repo.store_trained_model(self._model_name, model_dump, self._loss, self._accuracy, self._training_data_size, self._validation_data_size, self._start_time, end_time)          
            path.unlink()
    #def
#class


