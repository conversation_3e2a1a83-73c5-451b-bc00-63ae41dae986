#!/usr/bin/env python3

from typing import List
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository
from datetime import datetime, time, timedelta, timezone


class ProductSnapshot:

    SNAPSHOT_INTERVAL_HOURS = 12

    def __init__(self, product_repo: ProductRepository, eprice_repo: ElasticPriceRepository):
        self._product_repo = product_repo
        self._eprice_repo = eprice_repo
    #def

    @staticmethod
    def new_instance():
        return ProductSnapshot(ProductRepository.new_instance(), ElasticPriceRepository.new_instance())
    #def

    def load_product_optimization(self, customer_id:int, limit:int=1000, offset:int=0)->List[dict]:

        optimizations = self._product_repo.get_product_optimization(customer_id, limit, offset)
        # print('################# optimizations=',optimizations)

        if len(optimizations) == 0:
            return []
        

        optimization_data = []
        for row in optimizations:
            amazon_seller_ids = self._product_repo.get_amazon_seller(customer_id, row['marketplace_id'])

            if len(amazon_seller_ids) == 0:
                continue

            optimization_limits = self._product_repo.get_optimization_limit(customer_id, row['used_optimization_id'])

            if len(optimization_limits) == 0:
                continue

            d = {
                'product': row,
                'amazon_seller_ids' : amazon_seller_ids,
                'optimization_limits' : optimization_limits
            }

            optimization_data.append(d)
        #for

        return optimization_data
    #def

    def make_snapshot_by_customer(self, customer_id:int)->int:
        now = datetime.now(timezone.utc)

        last_snapshot = self._eprice_repo.get_last_product_optimization_snapshot(customer_id=customer_id)

        if last_snapshot is not None:
            snapshot_date_end = last_snapshot['snapshot_date_end']
            diff = now - snapshot_date_end
            hours = diff.total_seconds()/(60*60)

            print('##### customer_id=', customer_id)
            print('last_snapshot_date_end=', snapshot_date_end)
            print('now=', now)
            print('diff.hours=', hours)

            if hours < self.SNAPSHOT_INTERVAL_HOURS:
                return 0
        else:
            snapshot_date_end = datetime.now(timezone.utc) - timedelta(hours=self.SNAPSHOT_INTERVAL_HOURS)
        
        limit = 1000
        offset = 0
        count = 0
        while True:
            optimization_data = self.load_product_optimization(customer_id, limit, offset)
            # print('#################  optimization_data=', optimization_data)
            offset = offset + limit


            self._eprice_repo.store_product_optimization_snapshot(customer_id, optimization_data, snapshot_date_end, now)
            if len(optimization_data) > 0:
                count += 1

            if len(optimization_data) < limit:
                break
        #while

        if last_snapshot is not None and last_snapshot['amount'] == 0 :
            self._eprice_repo.delete_product_optimization_snapshot(last_snapshot['id'])
        #if
            

        return count      
    #def
    
    def make_snapshot(self)->int:
        customers =  self._product_repo.get_customer_ids()
        count = 0
        for customer in customers:
            count += self.make_snapshot_by_customer(int(customer['id']))

        return count
    #def
#class
