#!/usr/bin/env python3
"""
Test script to verify the StandardScaler warning fix
"""

import sys
import os
from pathlib import Path
import warnings

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
from domains.elasticprice.enhanced_price_model import SalesDataPreprocessor

def test_scaler_warning_fix():
    """Test that the StandardScaler warning is fixed"""
    print("=== Testing StandardScaler Warning Fix ===")
    
    # Create sample data
    sample_data = pd.DataFrame({
        'asin': ['B09TRB27ZP'] * 3,
        'sku': ['test-sku'] * 3,
        'order_marketplace_id': ['A1PA6795UKMFR9'] * 3,
        'total_quantity': [1, 2, 1],
        'total_price': [38.95, 77.90, 38.95],
        'prev_total_quantity': [1, 1, 2],
        'prev_total_price': [38.95, 38.95, 77.90],
        'price_per_quantity': [38.95, 38.95, 38.95],
        'prev_price_per_quantity': [38.95, 38.95, 38.95],
        'delta_quantity': [0, 1, -1],
        'delta_price_per_quantity': [0, 0, 0],
        'step_price_type': [1, 1, 0],
        'step_point': [0, 1, 0],
        'step_price': [1.5, 2.0, 1.0],
        'recommended_steps': [2, 3, 1]
    })
    
    print(f"Sample data shape: {sample_data.shape}")
    print(f"Columns: {list(sample_data.columns)}")
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # Initialize and fit preprocessor
        print("\n1. Fitting preprocessor...")
        preprocessor = SalesDataPreprocessor()
        preprocessor.fit(sample_data)
        print("✓ Fit completed")
        
        # Transform data
        print("\n2. Transforming data...")
        processed_data = preprocessor.transform(sample_data)
        print("✓ Transform completed")
        
        # Check for warnings
        sklearn_warnings = [warning for warning in w if 'sklearn' in str(warning.message).lower()]
        feature_name_warnings = [warning for warning in w if 'feature names' in str(warning.message).lower()]
        
        print(f"\n3. Warning Analysis:")
        print(f"   Total warnings: {len(w)}")
        print(f"   Sklearn warnings: {len(sklearn_warnings)}")
        print(f"   Feature name warnings: {len(feature_name_warnings)}")
        
        if feature_name_warnings:
            print("\n   ⚠️  Feature name warnings still present:")
            for warning in feature_name_warnings:
                print(f"      {warning.message}")
        else:
            print("\n   ✅ No feature name warnings!")
        
        # Test fit_transform method
        print("\n4. Testing fit_transform method...")
        preprocessor2 = SalesDataPreprocessor()
        processed_data2 = preprocessor2.fit_transform(sample_data)
        print("✓ fit_transform completed")
        
        # Verify data shapes
        print(f"\n5. Data verification:")
        print(f"   Numeric features shape: {processed_data['numeric_features'].shape}")
        print(f"   Categorical features: {list(processed_data['categorical_features'].keys())}")
        print(f"   Vocab sizes: {processed_data['vocab_sizes']}")
        
        # Test with missing features
        print("\n6. Testing with missing features...")
        incomplete_data = sample_data.drop(columns=['prev_total_quantity', 'prev_total_price'])
        
        with warnings.catch_warnings(record=True) as w2:
            warnings.simplefilter("always")
            processed_incomplete = preprocessor.transform(incomplete_data)
            
            missing_feature_warnings = [w for w in w2 if 'Missing numeric features' in str(w.message)]
            print(f"   Missing feature warnings: {len(missing_feature_warnings)}")
            if missing_feature_warnings:
                print(f"   ✓ Properly handled missing features")
    
    print("\n=== Test completed ===")

if __name__ == "__main__":
    test_scaler_warning_fix()
