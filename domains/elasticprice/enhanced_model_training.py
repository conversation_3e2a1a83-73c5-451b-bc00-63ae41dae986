"""
Enhanced Model Training Script

This script demonstrates how to train the enhanced price prediction model
using your sales history data with improved architecture and features.
"""

import sys
import os

# Add project root to path for imports
path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(path)

from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split

from domains.elasticprice.enhanced_price_model import (
    EnhancedPricePredictor,
    EnhancedLossFunction,
    SalesDataPreprocessor,
    EnhancedModelTrainer,
    save_enhanced_model,
    load_enhanced_model,
    predict_optimal_price
)


def preprocess_sales_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Enhanced data preprocessing with better feature engineering
    """
    print("Preprocessing sales data...")

    # Sort by product and date
    df = df.sort_values(['asin', 'sku', 'order_marketplace_id', 'order_purchase_date'])
    df = df.reset_index(drop=True)

    df['total_price'] = df['total_price'] / 100

    # Calculate temporal features
    df['prev_total_quantity'] = df.groupby(['asin', 'sku', 'order_marketplace_id'])['total_quantity'].shift(1)
    df['prev_total_price'] = df.groupby(['asin', 'sku', 'order_marketplace_id'])['total_price'].shift(1)
    df['prev_price_per_quantity'] = df['prev_total_price'] / df['prev_total_quantity']
    df['price_per_quantity'] = df['total_price'] / df['total_quantity']

    df = df.dropna(subset=['prev_total_quantity', 'prev_total_price', 'prev_price_per_quantity'])


    df['delta_price'] = df['total_price'] - df['total_price_previous']
    df['delta_quantity'] = df['total_quantity'] - df['prev_total_quantity']
    df['delta_price_per_quantity'] = df['price_per_quantity'] - df['prev_price_per_quantity']

    # Enhanced target engineering
    # 1. Price direction (UP/DOWN/NO_CHANGE) - now 3 classes
    # 0 = DOWN, 1 = NO_CHANGE, 2 = UP
    def classify_price_direction(row):
        delta_price = row['delta_price']
        delta_price_per_quantity = row['delta_price_per_quantity']  # Use price per quantity for more stable classification
        delta_quantity = row['delta_quantity']

        # Define thresholds for price change significance
        price_threshold = 0.10  # 10 cents threshold, 0.10 for more stable classification

        if abs(delta_price_per_quantity) <= price_threshold:
            return 1  # NO_CHANGE
        elif delta_price > 0 and delta_price_per_quantity > price_threshold and delta_quantity >= -2:
            return 2  # UP
        else:
            return 0  # DOWN

    df['step_price_type'] = df.apply(classify_price_direction, axis=1)

    # 2. Adjustment type (PERCENT/VALUE)
    df['pct_change'] = df['delta_price_per_quantity'] / df['prev_price_per_quantity'].replace(0, 1e-5)
    df['step_point'] = (df['pct_change'].abs() > 0.1).astype(int)

    # 3. Number of optimization steps (1-5)
    df['recommended_steps'] = (df['delta_quantity'].abs() / 2).clip(1, 5).round().astype(int)

    # 4. Price step magnitude
    df['step_price'] = df['delta_price_per_quantity'].abs().clip(0.01, 10)

    # 5. Maximum steps limit (1-10)
    # df['limit'] = (df['recommended_steps'] + 1).clip(1, 10)

    # Add additional features with better handling of NaN values
    df['price_volatility'] = df.groupby(['asin', 'sku', 'order_marketplace_id'])['price_per_quantity'].transform('std').fillna(0)
    df['quantity_trend'] = df.groupby(['asin', 'sku', 'order_marketplace_id'])['total_quantity'].transform(lambda x: x.diff().fillna(0))
    df['days_since_last_sale'] = df.groupby(['asin', 'sku', 'order_marketplace_id'])['order_purchase_date'].transform(lambda x: (pd.to_datetime(x) - pd.to_datetime(x).shift(1)).dt.days.fillna(0))

    # Fill any remaining NaN values
    df = df.fillna(0)

    # Ensure all numeric columns are finite
    numeric_cols = ['delta_quantity', 'delta_price_per_quantity', 'step_price', 'price_volatility', 'quantity_trend', 'days_since_last_sale']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = df[col].replace([np.inf, -np.inf], 0)
            df[col] = df[col].fillna(0)

    # Remove rows with missing previous day info for training
    # df_clean = df.dropna(subset=['prev_total_quantity', 'prev_total_price', 'prev_price_per_quantity']).copy()
    df_clean = df.copy()

    print(f"Processed {len(df_clean)} records from {len(df)} original records")
    return df_clean


class SimpleDataset:
    """Simple dataset class for our data"""
    def __init__(self, numeric_features, categorical_features, targets):
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.targets = targets
        self.length = len(numeric_features)

    def __len__(self):
        return self.length

    def __getitem__(self, idx):
        return {
            'numeric_features': self.numeric_features[idx],
            'categorical_features': {k: v[idx] for k, v in self.categorical_features.items()},
            'targets': {k: v[idx] for k, v in self.targets.items()}
        }


def create_data_loaders(processed_data, batch_size=32, test_size=0.2, val_size=0.1):
    """Create train, validation, and test data loaders"""

    # Extract features and targets
    numeric_features = processed_data['numeric_features']
    categorical_features = processed_data['categorical_features']
    targets = processed_data['targets']

    # Split data
    indices = np.arange(len(numeric_features))
    train_idx, test_idx = train_test_split(indices, test_size=test_size, random_state=42)
    train_idx, val_idx = train_test_split(train_idx, test_size=val_size, random_state=42)

    def create_loader(idx):
        dataset = SimpleDataset(
            numeric_features[idx],
            {k: v[idx] for k, v in categorical_features.items()},
            {k: v[idx] for k, v in targets.items()}
        )
        return DataLoader(dataset, batch_size=batch_size, shuffle=True)

    train_loader = create_loader(train_idx)
    val_loader = create_loader(val_idx)
    test_loader = create_loader(test_idx)

    return train_loader, val_loader, test_loader


def main():
    """Main training function"""

    print("=== Enhanced Price Prediction Model Training ===")
    print(f"Start time: {datetime.now()}")

    from domains.elasticprice.loader import Loader
    from datetime import timedelta

    # # Load raw data from last 10 days
    file_path = f'sales_data_{datetime.now().strftime("%Y%m%d")}.csv'

    loader = Loader.new_instance()
    loader.load_to_csv(file_path, datetime.now() - timedelta(days=10), datetime.now())

    # Load and preprocess data
    print("\n1. Loading sales data...")
    df = pd.read_csv(file_path)

    print(f"Loaded {len(df)} records")

    # Preprocess data
    df_processed = preprocess_sales_data(df)
    df_processed.to_csv(f'processed_data_{datetime.now().strftime("%Y%m%d")}.csv', index=False)

    # sys.exit(0)

    # Initialize preprocessor and fit on data
    print("\n2. Initializing data preprocessor...")
    preprocessor = SalesDataPreprocessor()
    processed_data = preprocessor.fit_transform(df_processed)

    print(f"Numeric features shape: {processed_data['numeric_features'].shape}")
    print(f"Vocabulary sizes: {processed_data['vocab_sizes']}")

    # Create data loaders
    print("\n3. Creating data loaders...")
    train_loader, val_loader, test_loader = create_data_loaders(processed_data)

    # Initialize model
    print("\n4. Initializing enhanced model...")
    model = EnhancedPricePredictor(
        numeric_dim=processed_data['numeric_features'].shape[1],
        vocab_sizes=processed_data['vocab_sizes'],
        embed_dim=32,
        hidden_dim=128,
        num_heads=8,
        dropout_rate=0.2
    )

    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Initialize loss function and trainer
    loss_function = EnhancedLossFunction()
    trainer = EnhancedModelTrainer(
        model=model,
        loss_function=loss_function,
        learning_rate=0.001,
        weight_decay=1e-5
    )

    # Train model
    print("\n5. Training model...")
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=100,
        patience=15
    )

    # Save model
    print("\n6. Saving model...")
    model_info = {
        'training_samples': len(train_loader.dataset),
        'validation_samples': len(val_loader.dataset),
        'final_train_loss': history['train_loss'][-1],
        'final_val_loss': history['val_loss'][-1],
        'best_metrics': history['val_metrics'][-1] if history['val_metrics'] else {}
    }

    save_enhanced_model(
        model=model,
        preprocessor=preprocessor,
        path='enhanced_price_model.pt',
        model_info=model_info
    )

    # model, preprocessor = load_enhanced_model('enhanced_price_model.pt')

    # Test prediction
    print("\n7. Testing prediction...")
    test_prediction = predict_optimal_price(
        model=model,
        preprocessor=preprocessor,
        asin='B0D5YRCMZY',
        sku='AG-GU-SW-4T',
        marketplace_id='A1PA6795UKMFR9',
        current_price=597.0,
        current_quantity=1,
        # historical_data=df_processed
    )

    print("Sample prediction:")
    for key, value in test_prediction.items():
        print(f"  {key}: {value}")

    print(f"\nTraining completed at: {datetime.now()}")
    print("Enhanced model saved successfully!")


def predict():
    model, preprocessor = load_enhanced_model('enhanced_price_model.pt')


    # Test prediction
    print("\n7. Testing prediction...")
    test_prediction = predict_optimal_price(
        model=model,
        preprocessor=preprocessor,
        asin='B09TRB27ZP',
        sku='3001-I-Fliegenfransen-Black-pony',
        marketplace_id='A1PA6795UKMFR9',
        current_price=9.94,
        current_quantity=3,
        # historical_data=df_processed
    )

    print("Sample prediction:")
    for key, value in test_prediction.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    # main()
    predict()