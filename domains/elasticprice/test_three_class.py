#!/usr/bin/env python3
"""
Test script for 3-class price direction (<PERSON>OW<PERSON>, NO_CHANGE, UP)
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

import torch
import pandas as pd
from domains.elasticprice.model_ep2 import ModelEp2Trainer, preprocess_data, predict_result
from domains.elasticprice.model import ElasticPriceNN


def test_three_class_price_direction():
    """Test the 3-class price direction functionality"""
    print("=== Testing 3-Class Price Direction ===")
    
    # Create sample data with different price scenarios
    sample_data = pd.DataFrame({
        'asin': ['TEST001'] * 6,
        'sku': ['test-sku'] * 6,
        'order_marketplace_id': ['A1PA6795UKMFR9'] * 6,
        'order_purchase_date': pd.date_range('2024-01-01', periods=6),
        'total_quantity': [2, 2, 2, 2, 2, 2],
        'total_price': [40.0, 40.0, 40.0, 40.0, 40.0, 40.0],
        'total_quantity_previous': [2, 2, 2, 2, 2, 2],
        'total_price_previous': [50.0, 40.05, 39.95, 35.0, 45.0, 40.0],  # Different price scenarios
    })
    
    print(f"Sample data shape: {sample_data.shape}")
    
    # Preprocess data
    print("\n1. Preprocessing data...")
    try:
        processed_data = preprocess_data(sample_data)
        print("✓ Preprocessing successful")
        
        # Check the price direction classifications
        print("\n2. Price direction classifications:")
        for i, row in processed_data.iterrows():
            delta_price = row['delta_price_per_quantity']
            price_type = row['step_price_type']
            price_type_str = {0: "DOWN", 1: "NO_CHANGE", 2: "UP"}[price_type]
            
            print(f"   Row {i}: delta_price={delta_price:.3f} -> {price_type_str} ({price_type})")
            
        # Verify we have all 3 classes
        unique_classes = processed_data['step_price_type'].unique()
        print(f"\n   Unique price direction classes: {sorted(unique_classes)}")
        
        if len(unique_classes) == 3:
            print("   ✓ All 3 classes (DOWN, NO_CHANGE, UP) are present")
        else:
            print(f"   ⚠️  Only {len(unique_classes)} classes found")
            
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        return
    
    # Test with a simple model
    print("\n3. Testing with model...")
    try:
        # Create a small model for testing
        dimensions = [3, 6, 4]  # Small model for testing
        model = ElasticPriceNN.create_new_model(
            dimensions=dimensions,
            dropout_rate=0.1,
            use_norm=True,
            use_attention=False,
            use_feature_fusion=False
        )
        
        print(f"✓ Model created with dimensions: {dimensions}")
        
        # Test prediction
        model.eval()
        test_prediction = predict_result(
            model=model,
            asin='TEST001',
            sku='test-sku',
            marketplace='A1PA6795UKMFR9'
        )
        
        if test_prediction:
            print("✓ Prediction successful")
            print(f"   Price direction: {test_prediction['step_price_type']}")
            print(f"   Step point: {test_prediction['step_point']}")
            print(f"   Step price: {test_prediction['step_price']}")
            print(f"   Recommended steps: {test_prediction['recommended_steps']}")
            
            # Verify the price direction is one of the valid options
            valid_directions = ["DOWN", "NO_CHANGE", "UP"]
            if test_prediction['step_price_type'] in valid_directions:
                print("   ✓ Valid price direction returned")
            else:
                print(f"   ✗ Invalid price direction: {test_prediction['step_price_type']}")
        else:
            print("✗ Prediction failed")
            
    except Exception as e:
        print(f"✗ Model testing failed: {e}")
        return
    
    print("\n=== Test completed ===")


def test_price_direction_logic():
    """Test the price direction classification logic"""
    print("\n=== Testing Price Direction Logic ===")
    
    # Test cases: (delta_price, delta_quantity, expected_class)
    test_cases = [
        (-0.50, -1, 0),   # DOWN: significant price decrease
        (-0.02, 0, 1),    # NO_CHANGE: small price change
        (0.03, 1, 1),     # NO_CHANGE: small price change
        (0.10, 2, 2),     # UP: significant price increase with good quantity
        (0.15, -3, 0),    # DOWN: price increase but bad quantity trend
        (0.00, 0, 1),     # NO_CHANGE: no price change
    ]
    
    print("Test cases:")
    print("delta_price | delta_quantity | expected | actual | result")
    print("-" * 55)
    
    all_passed = True
    for delta_price, delta_quantity, expected in test_cases:
        # Create a test row
        test_row = pd.Series({
            'delta_price_per_quantity': delta_price,
            'delta_quantity': delta_quantity
        })
        
        # Apply the classification function
        def classify_price_direction(row):
            delta_price = row['delta_price_per_quantity']
            delta_quantity = row['delta_quantity']
            
            price_threshold = 0.05
            
            if abs(delta_price) <= price_threshold:
                return 1  # NO_CHANGE
            elif delta_price > price_threshold and delta_quantity >= -2:
                return 2  # UP
            else:
                return 0  # DOWN
        
        actual = classify_price_direction(test_row)
        passed = actual == expected
        all_passed = all_passed and passed
        
        result_str = "✓" if passed else "✗"
        class_names = {0: "DOWN", 1: "NO_CHANGE", 2: "UP"}
        
        print(f"{delta_price:11.2f} | {delta_quantity:13d} | {class_names[expected]:8s} | {class_names[actual]:6s} | {result_str}")
    
    if all_passed:
        print("\n✓ All test cases passed!")
    else:
        print("\n✗ Some test cases failed!")
    
    return all_passed


if __name__ == "__main__":
    test_three_class_price_direction()
    test_price_direction_logic()
