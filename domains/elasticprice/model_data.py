#!/usr/bin/env python3

from typing import Any, Dict, List
import pandas as pd

def text_to_vec(label:str, size:int = -1) -> list:
    c_list= list(label)

    vec = []

    n = 0
    for c in c_list:
        l = c.strip()
        if (len(l) > 0) :
            n = 0
            vec.append(ord(l))
        else :
            if n == 0 :
                vec.append(0)
            
            n=+1
    # for
    lv = len(vec)

    if size > 0 :
        if lv > size :
            vec = vec[:size]
        
        if lv < size :
            for x in range(len(vec)+1, size + 1):
                vec.append(0)


    return vec
#def


def build_y_from_row(row:pd.Series)->list:
    result = []
    keys_prefix, keys = y_params()

    for i in range(5):
        for k, v in keys.items():
            # print(f'###### {keys_prefix}{k}_{i} ######')
            # print(row[f'{keys_prefix}{k}_{i}'])
            # print("is None=", row[f'{keys_prefix}{k}_{i}'] is None)
            # print('pd.isnull= ', pd.isnull(row[f'{keys_prefix}{k}_{i}']))
            # print('pd.isna= ', pd.isna(row[f'{keys_prefix}{k}_{i}']))

            if  pd.isna(row[f'{keys_prefix}{k}_{i}'])  or pd.isna(row[f'{keys_prefix}{k}_{i}']) or row[f'{keys_prefix}{k}_{i}'] is None :
                result.append(-1)
                continue

            if v['type'] == 'int' or v['type'] == 'float':
                result.append(
                    row[f'{keys_prefix}{k}_{i}']
                )
                continue

            if v['type'] == 'enum':
                for o in v['options']:
                    if row[f'{keys_prefix}{k}_{i}'] == o['enum_value']:
                        result.append(o['index'])
                        break
                else:
                    result.append(-1)
                continue
            
    return result
#def


def result_from_predicted(predicted:list)->tuple[list, list]:
    result = []
    result_predicted = []

    keys_prefix, keys = y_params()
    key_amount = len(keys)


    l = len(predicted)
    for i in range(5):  # 5 sets of parameters
        base_idx = i * key_amount  # Each set has 7 parameters

        if base_idx + key_amount >= l:
            break

        params  = {}
        params_predicted  = {}

        for k, v in keys.items():
            predict_value = predicted[base_idx]
            params_predicted[f'{k}'] = predict_value
            params[f'{k}'] = None

            if v['type'] == 'int':
                params[f'{k}'] = int(predict_value)
                base_idx += 1
                continue

            if v['type'] == 'float':
                params[f'{k}'] = round(predict_value, 2)
                base_idx += 1
                continue

            if v['type'] == 'enum':
                for o in v['options']:
                    if predict_value > o['predict_range'][0] and predict_value <= o['predict_range'][1]:
                        params[f'{k}'] = o['enum_value']
                        break
                base_idx += 1
                continue
        # for

        result_predicted.append(params_predicted)

        valid_params = 0
        for k, v in params.items():
            if k in keys :
                config = keys[k]
                if config['type'] == 'int' or config['type'] == 'float':
                    if v >= 0:
                        valid_params += 1
                        continue

                if config['type'] == 'enum':
                    if v is not None:
                        valid_params += 1
                        continue
            # if
        # for

        if valid_params >= key_amount :
            result.append(params)
    # for

    # if we have less than 5 results, we try to add more based on the existing ones
    # we try to add a DOWN parameter for each UP parameter
    # if len(result) < 5:
    #     down_exists = False
    #     min_up_limit = float('inf')
    #     min_up_dict= None
    #     for o in result:
    #         if o['step_price_type'] == 'UP':
    #             if o['limit'] < min_up_limit:
    #                 min_up_limit = o['limit']
    #                 min_up_dict = o

    #         if o['step_price_type'] == 'DOWN':
    #             down_exists = True

    #     if not down_exists and min_up_dict is not None:
    #         down_dict = min_up_dict.copy()
    #         down_dict['step_price_type'] = "DOWN"

    #         if down_dict['limit'] > 1:
    #             for l in range(1, down_dict['limit']):
    #                 result.append({**down_dict, 'limit': down_dict['limit'] - l})
    #                 if len(result) >= 5:
    #                     break
    #         else:
    #             result.append({**down_dict, 'limit': down_dict['limit'] - 1})

    
    return result, result_predicted
#def

def y_params():
    keys_prefix = 'optimization_limits__'
    keys = {
        'step_price' : {'type': 'float'},
        'step_point' : {
            'type': 'enum', 
            'options': [
                {'index': 0, 'enum_value': 'PERCENT', 'predict_range': [-0.3, 0.5]}, 
                {'index': 1, 'enum_value': 'VALUE', 'predict_range': [0.5, 100]}
            ],
        },
        'step_price_type' : {
            'type': 'enum',
            'options': [
                {'index': 0, 'enum_value': 'UP', 'predict_range': [-0.3, 0.5]}, 
                {'index': 1, 'enum_value': 'DOWN', 'predict_range': [0.5, 100]}
            ], 
        },
        'limit_type' : {
            'type': 'enum', 
            'options': [
                {'index': 0, 'enum_value': 'BEFORE', 'predict_range': [-0.3, 0.5]}, 
                {'index': 1, 'enum_value': 'AFTER', 'predict_range': [0.5, 100]}
            ], 
        },
        'limit' : {'type': 'int'},
        'limit_day': {'type': 'int'},
        'type': {
            'type': 'enum', 
            'options': [
                {'index': 0, 'enum_value': 'ORDER', 'predict_range': [-0.3, 0.5]}, 
                {'index': 1, 'enum_value': 'DAY', 'predict_range': [0.5, 100]}
            ], 
        },
    }

    return keys_prefix, keys
#def

def build_optimization_limit_params(optimizations: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:   
    params = {}
    keys_prefix, keys = y_params()
    
    for i in range(0, 5):
        for k in keys.keys():
            params[f'{keys_prefix}{k}_{i}'] = None
    
    for i, row in enumerate(optimizations):
        if i > 4:
            break
    
        for k in keys.keys():
            params[f'{keys_prefix}{k}_{i}'] = row[k]

    return params
#def
