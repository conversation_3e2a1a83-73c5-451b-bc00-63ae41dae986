import numpy as np
from io import BytesIO
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Optional
from datetime import datetime



def machine_device() -> torch.device:
    return torch.device("cuda" if torch.cuda.is_available() else "cpu")
#def

# Define a simple feedforward neural network
class ElasticPriceNN(nn.Module):
    NAME = "ElasticPriceNN"

    def __init__(self, dimensions: list[int], dropout_rate: float = 0.3, use_norm: bool = True,
                 use_attention: bool = True, use_feature_fusion: bool = True):
        super().__init__()

        if len(dimensions) < 2:
            raise ValueError("dimensions must have at least 2 elements")

        self.flatten = nn.Flatten()
        self._dimensions = dimensions
        input_dim = dimensions[0]
        hidden_dims = dimensions[1:-1]
        self.use_norm = use_norm
        self.use_attention = use_attention
        self.use_feature_fusion = use_feature_fusion

        # Input preprocessing with feature extraction
        self.input_preprocessing = nn.Sequential(
            nn.Linear(input_dim, input_dim),
            nn.LayerNorm(input_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5)  # Lower dropout for input
        )

        # Input layer with normalization
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )

        # Build enhanced hidden layers with residual connections
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_dims) - 1):
            self.hidden_layers.append(
                EnhancedResidualBlock(
                    hidden_dims[i],
                    hidden_dims[i+1],
                    dropout_rate=dropout_rate,
                    use_norm=use_norm,
                    use_attention=use_attention and i < 2  # Attention in first 2 layers
                )
            )

        # Feature fusion layer (combines features from different depths)
        if use_feature_fusion and len(hidden_dims) > 2:
            fusion_dim = hidden_dims[-1] + hidden_dims[-2] // 2
            self.feature_fusion = nn.Sequential(
                nn.Linear(fusion_dim, hidden_dims[-1]),
                nn.LayerNorm(hidden_dims[-1]) if use_norm else nn.Identity(),
                nn.GELU(),
                nn.Dropout(dropout_rate * 0.5)
            )
        else:
            self.feature_fusion = None

        # Specialized output heads for different prediction types
        final_dim = hidden_dims[-1]

        # Classification heads (step_price_type: 3 classes, step_point: 2 classes)
        self.price_type_head = nn.Sequential(
            nn.Linear(final_dim, final_dim // 2),
            nn.LayerNorm(final_dim // 2) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(final_dim // 2, 3)  # 3 classes: DOWN, NO_CHANGE, UP
        )

        self.step_point_head = nn.Sequential(
            nn.Linear(final_dim, final_dim // 2),
            nn.LayerNorm(final_dim // 2) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(final_dim // 2, 2)  # 2 classes: VALUE, PERCENT
        )

        # Continuous regression heads (step_price, step_steps)
        self.continuous_head = nn.Sequential(
            nn.Linear(final_dim, final_dim // 2),
            nn.LayerNorm(final_dim // 2) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(final_dim // 2, 2)  # 2 continuous outputs
        )

        # Initialize weights properly
        self._initialize_weights()

    def _initialize_weights(self):
        """Enhanced weight initialization for better gradient flow"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier initialization for GELU activations
                nn.init.xavier_uniform_(m.weight, gain=nn.init.calculate_gain('relu'))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)

    @property
    def dimensions(self):
        return self._dimensions

    def forward(self, x):
        x = self.flatten(x)

        # Input preprocessing
        x = self.input_preprocessing(x)
        x = self.input_layer(x)

        # Store intermediate features for fusion
        intermediate_features = []

        # Apply enhanced residual blocks
        for i, layer in enumerate(self.hidden_layers):
            x = layer(x)
            # Store features from later layers for fusion
            if self.feature_fusion and i >= len(self.hidden_layers) - 2:
                intermediate_features.append(x)

        # Feature fusion if enabled
        if self.feature_fusion and len(intermediate_features) >= 2:
            # Combine features from different depths
            fused_features = torch.cat([
                intermediate_features[-1],  # Latest features
                intermediate_features[-2][:, :intermediate_features[-2].size(1)//2]  # Earlier features (reduced)
            ], dim=1)
            x = self.feature_fusion(fused_features)

        # Specialized output heads
        price_type_logits = self.price_type_head(x)  # 3 classes: DOWN, NO_CHANGE, UP
        step_point_logits = self.step_point_head(x)  # 2 classes: VALUE, PERCENT
        continuous_outputs = self.continuous_head(x)  # step_price, step_steps

        # Apply appropriate activations for continuous outputs
        step_price = torch.relu(continuous_outputs[:, 0]) + 0.5  # step_price (0.5+ range)
        step_steps = torch.sigmoid(continuous_outputs[:, 1]) * 4 + 1  # step_steps (1-5 range)

        # Return dictionary with logits for training and processed values for inference
        if self.training:
            # During training, return logits for proper loss calculation
            return {
                'price_type_logits': price_type_logits,
                'step_point_logits': step_point_logits,
                'step_price': step_price,
                'step_steps': step_steps
            }
        else:
            # During inference, return processed values for compatibility
            output = torch.zeros(x.size(0), 4, device=x.device, dtype=x.dtype)
            output[:, 0] = torch.argmax(price_type_logits, dim=1).float()  # 0, 1, or 2
            output[:, 1] = torch.argmax(step_point_logits, dim=1).float()  # 0 or 1
            output[:, 2] = step_price
            output[:, 3] = step_steps
            return output
    #def

    @staticmethod
    def create_new_model(dimensions: list[int], dropout_rate: float = 0.3,
                        use_norm: bool = True, use_attention: bool = True,
                        use_feature_fusion: bool = True) -> 'ElasticPriceNN':
        return ElasticPriceNN(
            dimensions=dimensions,
            dropout_rate=dropout_rate,
            use_norm=use_norm,
            use_attention=use_attention,
            use_feature_fusion=use_feature_fusion
        )
    #def


    def save_model(self, path: str) -> None:
        try:
            state = {
                'model_state_dict': self.state_dict(),
                'model_dimensions': self.dimensions,
                'model_version': 2,  # Add version to track architecture changes
                'model_name': self.NAME,
                'created_at': datetime.now().isoformat()
            }

            torch.save(state, path)
            print("Saved PyTorch Model State to " + path)
        except Exception as e:
            print(f"Error saving model to {path}: {str(e)}")
    #def

    @staticmethod
    def load_model_dump(model_dump: bytes) -> Optional['ElasticPriceNN'] :
        try:
            state = torch.load(BytesIO(model_dump))
            
            # Get dimensions from saved model
            dimensions = state.get('model_dimensions')
            if not dimensions:
                print("Warning: No dimensions found in model dump")
                return None
                
            # Create a new model with the same architecture
            model = ElasticPriceNN(dimensions)
            
            # Handle potential architecture mismatch
            try:
                # Try strict loading first
                model.load_state_dict(state['model_state_dict'])
            except Exception as e:
                print(f"Warning: Strict loading failed, attempting flexible loading: {str(e)}")
                # Try non-strict loading (ignore missing/unexpected keys)
                model.load_state_dict(state['model_state_dict'], strict=False)
                
            return model
        except Exception as e:
            print(f"Error loading model from dump: {str(e)}")
            return None
    #def


    def predict(self, x_features:list) -> Optional[list]:
        '''
        Predicts the price for a given set of features.
        '''

        if len(x_features) != self.dimensions[0]:
            print(f"Error: Invalid input size. Expected {self.dimensions[0]}, got {len(x_features)}")
            return None

        model = self.eval()

        x = torch.tensor([x_features], dtype=torch.float32)

        with torch.no_grad():
            x = x.to(machine_device())
            predicted = model(x)

            return predicted.tolist()
        
        return None
    #def

    
#class


class ResidualBlock(nn.Module):
    """Residual block with skip connections for better gradient flow"""

    def __init__(self, in_dim: int, out_dim: int, dropout_rate: float = 0.3, use_norm: bool = True):
        super().__init__()

        # First sub-block
        self.layer1 = nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )

        # Second sub-block
        self.layer2 = nn.Sequential(
            nn.Linear(out_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )

        # Skip connection with projection if dimensions don't match
        self.skip = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()

    def forward(self, x):
        # Store input for skip connection
        identity = x

        # Forward pass through layers
        x = self.layer1(x)
        x = self.layer2(x)

        # Add skip connection
        x = x + self.skip(identity)

        return x


class EnhancedResidualBlock(nn.Module):
    """Enhanced residual block with optional attention mechanism"""

    def __init__(self, in_dim: int, out_dim: int, dropout_rate: float = 0.3,
                 use_norm: bool = True, use_attention: bool = False):
        super().__init__()

        self.use_attention = use_attention

        # Main transformation layers
        self.layer1 = nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )

        # Second transformation with bottleneck design
        bottleneck_dim = max(out_dim // 2, 16)
        self.layer2 = nn.Sequential(
            nn.Linear(out_dim, bottleneck_dim),
            nn.LayerNorm(bottleneck_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate * 0.5),  # Lower dropout in bottleneck
            nn.Linear(bottleneck_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
        )

        # Self-attention mechanism (optional)
        if use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=out_dim,
                num_heads=max(1, out_dim // 64),  # Dynamic number of heads
                dropout=dropout_rate * 0.5,
                batch_first=True
            )
            self.attention_norm = nn.LayerNorm(out_dim) if use_norm else nn.Identity()

        # Skip connection with projection if dimensions don't match
        self.skip = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()

        # Final activation
        self.final_activation = nn.GELU()

    def forward(self, x):
        # Store input for skip connection
        identity = x

        # Main transformation
        x = self.layer1(x)
        x = self.layer2(x)

        # Apply attention if enabled
        if self.use_attention:
            # Reshape for attention (add sequence dimension)
            x_att = x.unsqueeze(1)  # [batch, 1, features]
            x_att, _ = self.attention(x_att, x_att, x_att)
            x_att = x_att.squeeze(1)  # [batch, features]
            x = self.attention_norm(x + x_att)  # Residual connection for attention

        # Add skip connection
        x = x + self.skip(identity)

        # Final activation
        x = self.final_activation(x)

        return x
#class
    


# Training function
def train(dataloader, model, loss_fn, optimizer, device):
    # Modified train function with gradient clipping    
    # Add gradient clipping to prevent exploding gradients
    max_grad_norm = 1.0

    size = len(dataloader.dataset)
    current = 0
    model.train()
    epoch_loss = 0.0
    
    for batch_X, batch_y in dataloader:        
        batch_X, batch_y = batch_X.to(device), batch_y.to(device)
        
        # Check for NaN inputs
        if torch.isnan(batch_X).any() or torch.isnan(batch_y).any():
            print("WARNING: NaN values detected in input data!")
            continue
        
        optimizer.zero_grad()
        predictions = model(batch_X)
        loss = loss_fn(predictions, batch_y)
        
        # Skip backward pass if loss is NaN
        if torch.isnan(loss).any():
            print(f"WARNING: NaN loss detected! Skipping batch.")
            continue
            
        loss.backward()
        
        # Apply gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        
        optimizer.step()
        
        loss_val = loss.item()
        epoch_loss += loss_val
        current = current + len(batch_X)
        print(f"Train loss: {loss_val:>7f}  [{current:>5d}/{size:>5d}]")
#def


# Testing function
def test(dataloader, model, loss_fn, device)->tuple[float, float]:
    model.eval()
    test_loss = 0
    correct_classification = 0
    correct_continuous = 0
    total_samples = 0

    with torch.no_grad():
        for batch_X, batch_y in dataloader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            predictions = model(batch_X)
            test_loss += loss_fn(predictions, batch_y).item()

            # Mixed accuracy calculation
            batch_size = batch_y.size(0)
            total_samples += batch_size

            # Handle both dictionary and tensor formats
            if isinstance(predictions, dict):
                # New format with logits
                price_type_pred = torch.argmax(predictions['price_type_logits'], dim=1)
                step_point_pred = torch.argmax(predictions['step_point_logits'], dim=1)
                pred_continuous = torch.stack([predictions['step_price'], predictions['step_steps']], dim=1)
            else:
                # Legacy tensor format
                price_type_pred = predictions[:, 0].round()
                step_point_pred = predictions[:, 1].round()
                pred_continuous = predictions[:, 2:]

            # Classification accuracy (price_type: 3 classes, step_point: 2 classes)
            target_price_type = batch_y[:, 0].long()
            target_step_point = batch_y[:, 1].long()

            correct_classification += torch.sum(price_type_pred == target_price_type).item()
            correct_classification += torch.sum(step_point_pred == target_step_point).item()

            # Continuous/discrete regression accuracy (outputs 2, 3)
            target_continuous = batch_y[:, 2:]
            # Use relative error threshold for continuous values
            relative_error = torch.abs(pred_continuous - target_continuous) / (torch.abs(target_continuous) + 1e-8)
            correct_continuous += torch.sum(relative_error < 0.2).item()  # 20% tolerance

    avg_loss = test_loss / len(dataloader)

    # Combined accuracy: average of classification and continuous accuracies
    classification_accuracy = 100 * correct_classification / (total_samples * 2)  # 2 classification outputs
    continuous_accuracy = 100 * correct_continuous / (total_samples * 2)  # 2 continuous outputs
    overall_accuracy = (classification_accuracy + continuous_accuracy) / 2

    return avg_loss, overall_accuracy
#def


# Custom loss function for mixed outputs
class MixedOutputLoss(nn.Module):
    """
    Custom loss function for mixed output types:
    - Output 0: step_price_type (3-class classification: DOWN=0, NO_CHANGE=1, UP=2)
    - Output 1: step_point (binary classification: VALUE=0, PERCENT=1)
    - Output 2: step_price (continuous regression)
    - Output 3: step_steps (discrete regression)
    """
    def __init__(self, weights=None):
        super().__init__()
        # Balanced weights for each output component
        self.weights = weights if weights is not None else [2.0, 1.5, 1.0, 1.0]

        # Loss functions for different output types
        self.ce_loss = nn.CrossEntropyLoss()  # For multi-class classification
        self.bce_loss = nn.BCEWithLogitsLoss()  # For binary classification
        self.mse_loss = nn.MSELoss()  # For continuous regression
        self.smooth_l1_loss = nn.SmoothL1Loss()  # For discrete regression (more robust)

    def forward(self, predictions, targets):
        # Handle both dictionary and tensor formats
        if isinstance(predictions, dict):
            # New format with logits
            pred_price_type_logits = predictions['price_type_logits']  # 3-class logits
            pred_point_logits = predictions['step_point_logits']       # 2-class logits
            pred_price = predictions['step_price']                     # Continuous
            pred_steps = predictions['step_steps']                     # Discrete

            # Targets (assuming tensor format)
            target_price_type = targets[:, 0].long()  # 3-class targets (0, 1, 2)
            target_point = targets[:, 1].long()       # Binary targets (0, 1)
            target_price = targets[:, 2]              # Continuous
            target_steps = targets[:, 3]              # Discrete

            # Calculate individual losses
            loss_price_type = self.ce_loss(pred_price_type_logits, target_price_type)
            loss_point = self.ce_loss(pred_point_logits, target_point)

        else:
            # Legacy tensor format (for backward compatibility)
            pred_price_type = predictions[:, 0]  # Single values
            pred_point = predictions[:, 1]       # Single values
            pred_price = predictions[:, 2]       # Continuous
            pred_steps = predictions[:, 3]       # Discrete

            target_price_type = targets[:, 0]    # Single values
            target_point = targets[:, 1]         # Single values
            target_price = targets[:, 2]         # Continuous
            target_steps = targets[:, 3]         # Discrete

            # Calculate individual losses (using MSE for compatibility)
            loss_price_type = self.mse_loss(pred_price_type, target_price_type)
            loss_point = self.mse_loss(pred_point, target_point)

        # Common continuous losses
        loss_price = self.mse_loss(pred_price, target_price)
        loss_steps = self.smooth_l1_loss(pred_steps, target_steps)

        # Weighted combination
        total_loss = (
            self.weights[0] * loss_price_type +
            self.weights[1] * loss_point +
            self.weights[2] * loss_price +
            self.weights[3] * loss_steps
        )

        return total_loss


# Main training process
def train_model(model, train_dataset, test_dataset, epochs=10000, patience_limit=10, batch_size=1, learning_rate=0.001) -> tuple[ElasticPriceNN, float, float]:
    # Ensure batch_size is at least 1 for batch normalization to work
    if batch_size < 1:
        batch_size = min(1, len(train_dataset))
        print(f"Adjusted batch size to {batch_size} for stable training")
        
    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_dataloader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    device = machine_device()
    # Model, loss function, optimizer
    model = model.to(device)
    loss_fn = MixedOutputLoss()  # Use custom loss function for mixed outputs
    
    # Use a more stable optimizer configuration with gradient clipping
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, eps=1e-8, weight_decay=1e-5)

    # Add learning rate scheduler for better convergence
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=3, min_lr=1e-6)


    # Initialize weights with a more stable method
    def _reset_parameters(model):
        for m in model.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for linear layers
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    # Apply more stable initialization
    _reset_parameters(model)


    prev_loss = float('inf')
    prev_accuracy = 0
    best_model_state = None
    patience_counter = 0

    epoch_counter = 0
    while True:
        epoch_counter = epoch_counter + 1
        if epoch_counter > epochs:
            break

        print(f"Epoch {epoch_counter}\n")
        train(train_dataloader, model, loss_fn, optimizer, device)
        avg_loss, accuracy = test(test_dataloader, model, loss_fn, device)
        
        # Update learning rate based on validation loss
        scheduler.step(avg_loss)
        
        print(f"avg_loss={avg_loss}, accuracy={accuracy}")
        print(f"Test Loss: {avg_loss:.4f}, Test Accuracy: {accuracy:.2f}%")
        print(f"\n-------------------------------")

        # Improved early stopping: prioritize accuracy improvement
        improvement = False

        # Consider it an improvement if accuracy increases significantly
        # or if both accuracy and loss improve
        if accuracy > prev_accuracy + 0.5:  # Accuracy improved by at least 0.5%
            improvement = True
        elif accuracy > prev_accuracy - 1.0 and avg_loss < prev_loss * 0.95:  # Small accuracy drop but good loss improvement
            improvement = True

        if improvement:
            prev_loss = avg_loss
            prev_accuracy = accuracy
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            print(f"✓ Model improved! Accuracy: {accuracy:.2f}%, Loss: {avg_loss:.4f}")
        else:
            patience_counter += 1
            print(f"⏳ No improvement for {patience_counter} epochs")
            
        if patience_counter >= patience_limit:
            print(f"Early stopping triggered after {epoch_counter} epochs patience_limit={patience_limit}")
            break
    # while

    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    return model, prev_loss, prev_accuracy
#def
