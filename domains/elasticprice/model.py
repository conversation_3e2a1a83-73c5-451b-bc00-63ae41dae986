from io import BytesIO
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Optional
from datetime import datetime



def machine_device() -> torch.device:
    return torch.device("cuda" if torch.cuda.is_available() else "cpu")
#def

# Define a simple feedforward neural network
class ElasticPriceNN(nn.Module):
    NAME = "ElasticPriceNN"

    def __init__(self, dimensions: list[int], dropout_rate: float = 0.3, use_norm: bool = True):
        super().__init__()

        if len(dimensions) < 2:
            raise ValueError("dimensions must have at least 2 elements")

        self.flatten = nn.Flatten()
        self._dimensions = dimensions
        input_dim = dimensions[0]
        hidden_dims = dimensions[1:-1]
        output_dim = dimensions[-1]
        self.use_norm = use_norm

        # Input layer with normalization
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]) if use_norm else nn.Identity(),
            nn.GELU(),  # GELU activation (better than ReLU in many cases)
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )
        
        # Build hidden layers with residual connections
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_dims) - 1):
            self.hidden_layers.append(
                ResidualBlock(
                    hidden_dims[i], 
                    hidden_dims[i+1], 
                    dropout_rate=dropout_rate,
                    use_norm=use_norm
                )
            )
        
        # Output layer
        self.output_layer = nn.Linear(hidden_dims[-1], output_dim)
        
        # Initialize weights properly
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights using He initialization for better gradient flow"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    @property
    def dimensions(self):
        return self._dimensions

    def forward(self, x):
        x = self.flatten(x)
        x = self.input_layer(x)

        # Apply residual blocks
        for layer in self.hidden_layers:
            x = layer(x)

        # Output layer
        raw_output = self.output_layer(x)

        # Apply appropriate activations for mixed outputs
        # Outputs 0,1: Binary classification (no activation here, will use BCEWithLogitsLoss)
        # Output 2: step_price (continuous, positive) - use ReLU + offset
        # Output 3: step_steps (discrete 1-5) - use sigmoid scaled to 1-5 range

        output = torch.zeros_like(raw_output)
        output[:, 0] = raw_output[:, 0]  # step_price_type (logits)
        output[:, 1] = raw_output[:, 1]  # step_point (logits)
        output[:, 2] = torch.relu(raw_output[:, 2]) + 0.5  # step_price (0.5-10 range)
        output[:, 3] = torch.sigmoid(raw_output[:, 3]) * 4 + 1  # step_steps (1-5 range)

        return output
    #def

    @staticmethod
    def create_new_model(dimensions: list[int]) -> 'ElasticPriceNN':
        return ElasticPriceNN(dimensions)
    #def


    def save_model(self, path: str) -> None:
        try:
            state = {
                'model_state_dict': self.state_dict(),
                'model_dimensions': self.dimensions,
                'model_version': 2,  # Add version to track architecture changes
                'model_name': self.NAME,
                'created_at': datetime.now().isoformat()
            }

            torch.save(state, path)
            print("Saved PyTorch Model State to " + path)
        except Exception as e:
            print(f"Error saving model to {path}: {str(e)}")
    #def

    @staticmethod
    def load_model_dump(model_dump: bytes) -> Optional['ElasticPriceNN'] :
        try:
            state = torch.load(BytesIO(model_dump))
            
            # Get dimensions from saved model
            dimensions = state.get('model_dimensions')
            if not dimensions:
                print("Warning: No dimensions found in model dump")
                return None
                
            # Create a new model with the same architecture
            model = ElasticPriceNN(dimensions)
            
            # Handle potential architecture mismatch
            try:
                # Try strict loading first
                model.load_state_dict(state['model_state_dict'])
            except Exception as e:
                print(f"Warning: Strict loading failed, attempting flexible loading: {str(e)}")
                # Try non-strict loading (ignore missing/unexpected keys)
                model.load_state_dict(state['model_state_dict'], strict=False)
                
            return model
        except Exception as e:
            print(f"Error loading model from dump: {str(e)}")
            return None
    #def


    def predict(self, x_features:list) -> Optional[list]:
        '''
        Predicts the price for a given set of features.
        '''

        if len(x_features) != self.dimensions[0]:
            print(f"Error: Invalid input size. Expected {self.dimensions[0]}, got {len(x_features)}")
            return None

        model = self.eval()

        x = torch.tensor([x_features], dtype=torch.float32)

        with torch.no_grad():
            x = x.to(machine_device())
            predicted = model(x)

            return predicted.tolist()
        
        return None
    #def

    
#class


class ResidualBlock(nn.Module):
    """Residual block with skip connections for better gradient flow"""
    
    def __init__(self, in_dim: int, out_dim: int, dropout_rate: float = 0.3, use_norm: bool = True):
        super().__init__()
        
        # First sub-block
        self.layer1 = nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )
        
        # Second sub-block
        self.layer2 = nn.Sequential(
            nn.Linear(out_dim, out_dim),
            nn.LayerNorm(out_dim) if use_norm else nn.Identity(),
            nn.GELU(),
            nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()
        )
        
        # Skip connection with projection if dimensions don't match
        self.skip = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()
        
    def forward(self, x):
        # Store input for skip connection
        identity = x
        
        # Forward pass through layers
        x = self.layer1(x)
        x = self.layer2(x)
        
        # Add skip connection
        x = x + self.skip(identity)
        
        return x
#class
    


# Training function
def train(dataloader, model, loss_fn, optimizer, device):
    # Modified train function with gradient clipping    
    # Add gradient clipping to prevent exploding gradients
    max_grad_norm = 1.0

    size = len(dataloader.dataset)
    current = 0
    model.train()
    epoch_loss = 0.0
    
    for batch_X, batch_y in dataloader:        
        batch_X, batch_y = batch_X.to(device), batch_y.to(device)
        
        # Check for NaN inputs
        if torch.isnan(batch_X).any() or torch.isnan(batch_y).any():
            print("WARNING: NaN values detected in input data!")
            continue
        
        optimizer.zero_grad()
        predictions = model(batch_X)
        loss = loss_fn(predictions, batch_y)
        
        # Skip backward pass if loss is NaN
        if torch.isnan(loss).any():
            print(f"WARNING: NaN loss detected! Skipping batch.")
            continue
            
        loss.backward()
        
        # Apply gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        
        optimizer.step()
        
        loss_val = loss.item()
        epoch_loss += loss_val
        current = current + len(batch_X)
        print(f"Train loss: {loss_val:>7f}  [{current:>5d}/{size:>5d}]")
#def


# Testing function
def test(dataloader, model, loss_fn, device)->tuple[float, float]:
    model.eval()
    test_loss = 0
    correct_binary = 0
    correct_continuous = 0
    total_samples = 0

    with torch.no_grad():
        for batch_X, batch_y in dataloader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            predictions = model(batch_X)
            test_loss += loss_fn(predictions, batch_y).item()

            # Mixed accuracy calculation
            batch_size = batch_y.size(0)
            total_samples += batch_size

            # Binary classification accuracy (outputs 0, 1)
            pred_binary = torch.sigmoid(predictions[:, :2])  # Apply sigmoid for binary outputs
            target_binary = batch_y[:, :2]
            correct_binary += torch.sum((pred_binary > 0.5) == (target_binary > 0.5)).item()

            # Continuous/discrete regression accuracy (outputs 2, 3)
            pred_continuous = predictions[:, 2:]
            target_continuous = batch_y[:, 2:]
            # Use relative error threshold for continuous values
            relative_error = torch.abs(pred_continuous - target_continuous) / (torch.abs(target_continuous) + 1e-8)
            correct_continuous += torch.sum(relative_error < 0.2).item()  # 20% tolerance

    avg_loss = test_loss / len(dataloader)

    # Combined accuracy: average of binary and continuous accuracies
    binary_accuracy = 100 * correct_binary / (total_samples * 2)  # 2 binary outputs
    continuous_accuracy = 100 * correct_continuous / (total_samples * 2)  # 2 continuous outputs
    overall_accuracy = (binary_accuracy + continuous_accuracy) / 2

    return avg_loss, overall_accuracy
#def


# Custom loss function for mixed outputs
class MixedOutputLoss(nn.Module):
    """
    Custom loss function for mixed output types:
    - Output 0: step_price_type (binary classification)
    - Output 1: step_point (binary classification)
    - Output 2: step_price (continuous regression)
    - Output 3: step_steps (discrete regression)
    """
    def __init__(self, weights=None):
        super().__init__()
        # Default weights for each output component
        self.weights = weights if weights is not None else [1.0, 1.0, 2.0, 1.5]

        # Loss functions for different output types
        self.bce_loss = nn.BCEWithLogitsLoss()  # For binary classification
        self.mse_loss = nn.MSELoss()  # For continuous regression
        self.smooth_l1_loss = nn.SmoothL1Loss()  # For discrete regression (more robust)

    def forward(self, predictions, targets):
        # Split predictions and targets
        pred_price_type = predictions[:, 0]  # Binary
        pred_point = predictions[:, 1]       # Binary
        pred_price = predictions[:, 2]       # Continuous
        pred_steps = predictions[:, 3]       # Discrete

        target_price_type = targets[:, 0]    # Binary
        target_point = targets[:, 1]         # Binary
        target_price = targets[:, 2]         # Continuous
        target_steps = targets[:, 3]         # Discrete

        # Calculate individual losses
        loss_price_type = self.bce_loss(pred_price_type, target_price_type)
        loss_point = self.bce_loss(pred_point, target_point)
        loss_price = self.mse_loss(pred_price, target_price)
        loss_steps = self.smooth_l1_loss(pred_steps, target_steps)

        # Weighted combination
        total_loss = (
            self.weights[0] * loss_price_type +
            self.weights[1] * loss_point +
            self.weights[2] * loss_price +
            self.weights[3] * loss_steps
        )

        return total_loss


# Main training process
def train_model(model, train_dataset, test_dataset, epochs=10000, patience_limit=10, batch_size=1, learning_rate=0.001) -> tuple[ElasticPriceNN, float, float]:
    # Ensure batch_size is at least 1 for batch normalization to work
    if batch_size < 1:
        batch_size = min(1, len(train_dataset))
        print(f"Adjusted batch size to {batch_size} for stable training")
        
    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_dataloader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    device = machine_device()
    # Model, loss function, optimizer
    model = model.to(device)
    loss_fn = MixedOutputLoss()  # Use custom loss function for mixed outputs
    
    # Use a more stable optimizer configuration with gradient clipping
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, eps=1e-8, weight_decay=1e-5)

    # Add learning rate scheduler for better convergence
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)


    # Initialize weights with a more stable method
    def _reset_parameters(model):
        for m in model.modules():
            if isinstance(m, nn.Linear):
                # Use Xavier/Glorot initialization for linear layers
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    # Apply more stable initialization
    _reset_parameters(model)


    prev_loss = float('inf')
    prev_accuracy = 0
    best_model_state = None
    patience_counter = 0

    epoch_counter = 0
    while True:
        epoch_counter = epoch_counter + 1
        if epoch_counter > epochs:
            break

        print(f"Epoch {epoch_counter}\n")
        train(train_dataloader, model, loss_fn, optimizer, device)
        avg_loss, accuracy = test(test_dataloader, model, loss_fn, device)
        
        # Update learning rate based on validation loss
        scheduler.step(avg_loss)
        
        print(f"avg_loss={avg_loss}, accuracy={accuracy}")
        print(f"Test Loss: {avg_loss:.4f}, Test Accuracy: {accuracy:.2f}%")
        print(f"\n-------------------------------")

        # Early stopping check
        if avg_loss < prev_loss and accuracy > prev_accuracy:
            prev_loss = avg_loss
            prev_accuracy = accuracy
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience_limit:
            print(f"Early stopping triggered after {epoch_counter} epochs patience_limit={patience_limit}")
            break
    # while

    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    return model, prev_loss, prev_accuracy
#def
