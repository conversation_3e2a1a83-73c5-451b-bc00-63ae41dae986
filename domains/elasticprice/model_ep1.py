import pandas as pd
import numpy as np
from sklearn import preprocessing

from domains.elasticprice.model_data import build_y_from_row, text_to_vec
from domains.elasticprice.model_ep import ElasticPriceModel



class ElasticPrice1Model(ElasticPriceModel):

    def __init__(self):
        super().__init__()
        self._model_name = 'ep1'
        self._dimensions = [204, 512, 256, 128, 64, 35]
    #def

    @staticmethod
    def build_x_feature(title: str, marketplace_id: str, quantity: int, stock: int, item_price: float) -> list:
        
        title_arr = np.array(text_to_vec(title, 200))
        title_normalized = preprocessing.normalize([title_arr])
        feature  = title_normalized[0].tolist()

        feature.append(sum(text_to_vec(marketplace_id)))
        feature.append(quantity)
        feature.append(stock)
        feature.append(item_price)

        return feature
    # def


    def _build_x_y(self, df:pd.DataFrame)->tuple[list, list] :
        features = []
        predictions = []

        for index, row in df.iterrows():
            if row["item_price"] <= 0:
                continue

            features.append(self.build_x_feature(row['title'], row['order_marketplace_id'], row['total_quantity'], row['stock'], row['item_price']))        
            predictions.append(build_y_from_row(row))
        #for

        return features, predictions
    #def
#class