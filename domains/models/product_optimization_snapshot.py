
from dataclasses import dataclass
from datetime import datetime

from lib.json import json_decode, json_encode


@dataclass
class ProductOptimizationSnapshot:
    id: int
    customer_id: int
    snapshot_date_start: datetime
    snapshot_date_end: datetime
    amount: int
    data: list[dict]

    def to_dict(self):
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'snapshot_date_start': self.snapshot_date_start,
            'snapshot_date_end': self.snapshot_date_end,
            'amount': self.amount,
            'data': self.data
        }
    #def

    def to_json(self):
        return json_encode(self.to_dict())
    #def

    @staticmethod
    def from_json(json_str: str):
        d = json_decode(json_str)

        if d is None:
            raise ValueError("Invalid JSON format")

        if isinstance(d, list):
            return ProductOptimizationSnapshot.from_dicts(d)
        elif isinstance(d, dict):
            return ProductOptimizationSnapshot.from_dict(d)
        
        raise ValueError("Invalid JSON format")
    #def


    @staticmethod
    def from_dict(d: dict):

        data = []
        if 'data_json' in d :
            if isinstance(d['data_json'], str):
                data = json_decode(d['data_json'])
            elif isinstance(d['data_json'], list):
                data = d['data_json']
            else:
                data = []

        
        if 'data' in d and isinstance(d['data'], list):
            data = d['data']


        return ProductOptimizationSnapshot(
            id=d['id'],
            customer_id=d['customer_id'],
            snapshot_date_start=d['snapshot_date_start'],
            snapshot_date_end=d['snapshot_date_end'],
            amount=d['amount'],
            data= data
        )
    #def

    @staticmethod
    def from_dicts(ds: list[dict]):
        return [ProductOptimizationSnapshot.from_dict(d) for d in ds]
    #def
#class

