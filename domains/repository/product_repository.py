#!/usr/bin/env python3

from typing import Any, Dict, List, Optional
from facades.redis.redisdb import <PERSON>isFacade
from facades.repricer.repricerdb import RepricerDbFacade
from lib.json import json_decode, json_encode


class ProductRepository:
    def __init__(self, repricer_conn: RepricerDbFacade, redis_cache: RedisFacade):
        self._cache_db = 1
        self._repricer_conn = repricer_conn
        self._redis_cache = redis_cache
    #def

    @staticmethod
    def new_instance():
        return ProductRepository(RepricerDbFacade.instance(), RedisFacade.instance())
    #def

    def get_customer_ids(self)->list[int]:
        return self._repricer_conn.main_db_query() \
            .query_all("SELECT id FROM customer WHERE active = 1  AND use_repricer_module = 1  ORDER BY id ASC")
    #def
    
    def get_product_optimization(self, customer_id: int, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get product optimization data.
        
        Args:
            customer_id: Customer ID
            limit: Maximum number of records to return
            offset: Offset for pagination
            
        Returns:
            List of product optimization records
        """
        q = """
        SELECT 
            T1.optimization_active,
            T1.optimization_id,
            T1.optimization_template_id,
            T1.offer_type,
            T2.id,
            T2.asin,
            T2.sku,
            T2.title,
            T2.marketplace_id,
            T2.stock_type,
            T2.deleted,
            T3.id as T3_id,
            T4.id as T4_id,
            T4.optimization_id,
            T5.id as T5_id,
            COALESCE(T5.id , T3.id) as used_optimization_id,
            T6.stock, 
            T6.local_stock
            
        FROM amazon_product_offer_v2 T1

        INNER JOIN amazon_product_v2 T2 ON T2.id = T1.id
        INNER JOIN amazon_product_stock_v2 T6 ON T6.id = T2.id and T6.stock_type = T2.stock_type
        LEFT JOIN optimization_v2 T3 ON T3.id = T1.optimization_id and T3.strategy = 'dailyPush'
        LEFT JOIN optimization_template_v2 T4 ON T4.id = T1.optimization_template_id
        LEFT JOIN optimization_v2 T5 ON T5.id = T4.optimization_id and T5.strategy = 'dailyPush'

        WHERE T1.optimization_active = 1 and T2.deleted = 0 and (T3.id is not null or T5.id is not null)
        ORDER BY T2.id ASC
        LIMIT  {offset}, {limit}
        """ 

        query = q.format(**{'offset': offset, 'limit': limit})
        # print('get_product_optimization: sql=', query)

        return self._repricer_conn.client_db_query(customer_id).query_all(query)
    #def
    
    def get_optimization_limit(self, customer_id: int, optimization_id: int) -> List[Dict[str, Any]]:
        """
        Get optimization limits with caching.
        
        Args:
            customer_id: Customer ID
            optimization_id: Optimization ID
            
        Returns:
            List of optimization limit records
        """
        key = f"optimization_limit_{customer_id}_{optimization_id}"
        if self._redis_cache.db_query(self._cache_db).exists(key):
            l =  json_decode(self._redis_cache.db_query(self._cache_db).get(key))

            if l is not None:
                return l

        q = """
        SELECT *        
        FROM optimization_limit_v2 
        WHERE optimization_id  = {optimization_id}
        ORDER BY id ASC
        """ 

        query = q.format(**{'optimization_id': optimization_id})
        # print('get_optimization_limit: sql=', query)

        result = self._repricer_conn.client_db_query(customer_id).query_all(query)

        self._redis_cache.db_query(self._cache_db).set(key, json_encode(result), 600)

        return result
    #def
    
    def get_amazon_seller(self, customer_id: int, marketplace_id: str) -> List[Dict[str, Any]]:
        """
        Get Amazon seller information with caching.
        
        Args:
            customer_id: Customer ID
            marketplace_id: Marketplace ID
            
        Returns:
            List of Amazon seller records
        """
        key = f"amazon_seller_{customer_id}_{marketplace_id}"
        if self._redis_cache.db_query(self._cache_db).exists(key):
            l =  json_decode(self._redis_cache.db_query(self._cache_db).get(key))

            if l is not None:
                return l

        q = """
        SELECT *        
        FROM amazon_seller 
        WHERE marketplace_id = '{marketplace_id}' and customer_id = {customer_id}
        """ 

        query = q.format(**{'marketplace_id': marketplace_id, 'customer_id': customer_id})
        # print('get_amazon_seller: sql=', query)

        result = self._repricer_conn.main_db_query().query_all(query)

        self._redis_cache.db_query(self._cache_db).set(key, json_encode(result), 600)

        return result
    #def
#class

