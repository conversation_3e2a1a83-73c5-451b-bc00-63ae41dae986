services:
  cli:
    image: {IMGTAG}
    restart: always
    command: ["python", "-m", "cli.ml", "elastic-price", "train"]
    networks:
      - elastic-price
    logging:
      options:
        max-size: "50m"
        max-file: "3"
    volumes:
      - /data/data:/app/data
      - /data/logs:/app/logs

networks:
    elastic-price:
        driver: bridge
        driver_opts:
            com.docker.network.driver.mtu: 1450
