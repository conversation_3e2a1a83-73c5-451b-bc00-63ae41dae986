# """User repository for database operations."""
# import logging
# from typing import List, Optional, Dict, Any
# from myproject.core.models import User
# from myproject.db.repositories.base import BaseRepository

# # Configure logging
# # logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)

# class UserRepository(BaseRepository[User]):
#     """Repository for User model database operations."""
    
#     def __init__(self):
#         """Initialize the user repository."""
#         super().__init__(User)
    
#     def _get_table_name(self) -> str:
#         """Override to use 'users' as table name."""
#         return "users"
    
#     def find_by_username(self, username: str) -> Optional[User]:
#         """
#         Find a user by username.
        
#         Args:
#             username: The username to look for
            
#         Returns:
#             User instance or None if not found
#         """
#         logger.info(f"Finding user with username '{username}'")
#         # In a real implementation, this would execute a query
#         # For demonstration purposes:
#         return None
    
#     def find_by_email(self, email: str) -> Optional[User]:
#         """
#         Find a user by email.
        
#         Args:
#             email: The email to look for
            
#         Returns:
#             User instance or None if not found
#         """
#         logger.info(f"Finding user with email '{email}'")
#         # In a real implementation, this would execute a query
#         return None
    
#     def find_active_users(self) -> List[User]:
#         """
#         Find all active users.
        
#         Returns:
#             List of active User instances
#         """
#         logger.info("Finding all active users")
#         # In a real implementation, this would execute a query
#         return []
    
#     def create(self, user: User) -> User:
#         """
#         Create a new user.
        
#         Args:
#             user: The user to create
            
#         Returns:
#             Created user with ID
#         """
#         logger.info(f"Creating new user with username '{user.username}'")
#         # In a real implementation, this would execute an insert query
#         # and set the ID of the user
#         user.id = 1  # Simulating ID assignment
#         return user
    
#     def update(self, user: User) -> Optional[User]:
#         """
#         Update an existing user.
        
#         Args:
#             user: The user to update
            
#         Returns:
#             Updated user or None if not found
#         """
#         logger.info(f"Updating user with ID {user.id}")
#         # In a real implementation, this would execute an update query
#         return user
    
#     def deactivate(self, user_id: int) -> bool:
#         """
#         Deactivate a user instead of deleting.
        
#         Args:
#             user_id: The ID of the user to deactivate
            
#         Returns:
#             True if deactivated, False if not found
#         """
#         logger.info(f"Deactivating user with ID {user_id}")
#         # In a real implementation, this would execute an update query
#         # to set is_active = False
#         return True