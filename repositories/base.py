# """Base repository class for database operations."""
# import logging
# from typing import Generic, TypeVar, List, Optional, Any, Dict, Type
# from db.connection import DatabaseConnection

# # Configure logging
# # logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)

# # Generic type for models
# T = TypeVar('T')

# class BaseRepository(Generic[T]):
#     """
#     Base repository class that provides common database operations.
    
#     This class should be extended by specific repository implementations.
#     """
    
#     def __init__(self, model_class: Type[T]):
#         """
#         Initialize the repository with a model class.
        
#         Args:
#             model_class: The class of the model this repository handles
#         """
#         self.model_class = model_class
#         self.db_connection = DatabaseConnection().get_connection()
#         self.table_name = self._get_table_name()
#         logger.info(f"Initialized repository for {self.table_name}")
    
#     def _get_table_name(self) -> str:
#         """
#         Get the table name for the model.
        
#         By default, uses the lowercase model class name.
#         Override this method in subclasses if needed.
        
#         Returns:
#             Table name as string
#         """
#         return self.model_class.__name__.lower()
    
#     def find_all(self) -> List[T]:
#         """
#         Find all records.
        
#         Returns:
#             List of model instances
#         """
#         logger.info(f"Finding all records in {self.table_name}")
#         # In a real implementation, this would execute a query
#         # For demonstration purposes, return an empty list
#         return []
    
#     def find_by_id(self, id: int) -> Optional[T]:
#         """
#         Find a record by ID.
        
#         Args:
#             id: The ID to look for
            
#         Returns:
#             Model instance or None if not found
#         """
#         logger.info(f"Finding record with ID {id} in {self.table_name}")
#         # In a real implementation, this would execute a query
#         return None
    
#     def create(self, entity: T) -> T:
#         """
#         Create a new record.
        
#         Args:
#             entity: The entity to create
            
#         Returns:
#             Created entity with ID
#         """
#         logger.info(f"Creating new record in {self.table_name}")
#         # In a real implementation, this would execute an insert query
#         return entity
    
#     def update(self, entity: T) -> Optional[T]:
#         """
#         Update an existing record.
        
#         Args:
#             entity: The entity to update
            
#         Returns:
#             Updated entity or None if not found
#         """
#         logger.info(f"Updating record in {self.table_name}")
#         # In a real implementation, this would execute an update query
#         return entity
    
#     def delete(self, id: int) -> bool:
#         """
#         Delete a record by ID.
        
#         Args:
#             id: The ID of the record to delete
            
#         Returns:
#             True if deleted, False if not found
#         """
#         logger.info(f"Deleting record with ID {id} from {self.table_name}")
#         # In a real implementation, this would execute a delete query
#         return True
    
#     def count(self) -> int:
#         """
#         Count all records.
        
#         Returns:
#             Number of records
#         """
#         logger.info(f"Counting records in {self.table_name}")
#         # In a real implementation, this would execute a count query
#         return 0