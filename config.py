"""Configuration settings for the application."""
import os
from typing import List
from pydantic_settings import BaseSettings
from utils.env import load_env_file
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings."""
    # API settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = False
    ROOT_DIR: str = os.path.dirname(os.path.abspath(__file__))
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS settings
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"

   
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._env_vars = {}
        self._load_env(os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env'))

    def _load_env(self, env_file: str = ".env") -> None:
        # export_env_to_os(env_file)
        self._env_vars = load_env_file(env_file)

    def env(self, key: str, default: str = None) -> str:
        """Get environment variable value."""
        if key in self._env_vars:
            return self._env_vars[key]
        
        self._env_vars[key] = os.environ.get(key, default)

        return self._env_vars[key]
        

# # Simple singleton pattern
# _settings_instance = None

# def settings():
#     global _settings_instance
#     if _settings_instance is None:
#         print('settings called')
#         _settings_instance = Settings()
#     return _settings_instance


@lru_cache
def settings():
    return Settings()

