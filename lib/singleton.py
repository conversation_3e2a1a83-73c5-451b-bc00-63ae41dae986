#!/usr/bin/env python3
import threading


class SingletonDoubleChecked(object):

    # resources shared by each and every
    # instance

    __singleton_lock = threading.Lock()
    __singleton_instance = None

    # define the classmethod
    @classmethod
    def instance(cls):

        # check for the singleton instance
        if not cls.__singleton_instance:
            with cls.__singleton_lock:
                if not cls.__singleton_instance:
                    cls.__singleton_instance = cls()
                    cls.__singleton_instance.build_instance()

        # return the singleton instance
        return cls.__singleton_instance
    
    def build_instance(self):
        pass
#class