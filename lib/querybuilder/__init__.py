#!/usr/bin/env python3


from .postgre import PostgreSQLQueryBuilder
from .mysql import MySQLQueryBuilder
from .clickhouse import ClickHouseQueryBuilder


class QueryBuilder:
    """
    A secure SQL query builder that uses parameterized queries to prevent SQL injection.
    Factory class that returns the appropriate dialect-specific implementation.
    """
    
    def __new__(cls, dialect="mysql"):
        """
        Factory method to create the appropriate query builder.
        
        Args:
            dialect: Database dialect ('mysql' or 'postgresql')
        """
        dialect = dialect.lower()
        
        if dialect == "postgresql":
            return PostgreSQLQueryBuilder()
        elif dialect == "mysql":
            return MySQLQueryBuilder()
        
        elif dialect == "clickhouse":
            return ClickHouseQueryBuilder()
        else:
            raise ValueError(f"Unsupported dialect: {dialect}. Use 'mysql' or 'postgresql'.")

