#!/usr/bin/env python3

import logging
from typing import Any
from .base import BaseQueryBuilder

# Configure logging
logger = logging.getLogger(__name__)

class MySQLQueryBuilder(BaseQueryBuilder):
    """MySQL-specific query builder implementation."""
    
    def _get_param_placeholder(self, value):
        """Get MySQL parameter placeholder."""
        self._param_counter += 1
        param_name = f"p{self._param_counter}"
        self._parameters[param_name] = value
        return f"%({param_name})s"
    
    def _get_subquery_placeholder(self, param_name: str) -> str:
        """Get MySQL subquery placeholder format."""
        return f"%({param_name})s"
    
    def _build_limit_offset_clause(self) -> str:
        """Build MySQL LIMIT/OFFSET clause."""
        if self._limit is None:
            return ""
        
        limit_clause = f"LIMIT {self._limit}"
        if self._offset is not None:
            limit_clause += f" OFFSET {self._offset}"
        
        return limit_clause
    
    def _format_value_for_raw_sql(self, param_value: Any) -> str:
        """Format a value for MySQL raw SQL."""
        if isinstance(param_value, str):
            # Escape single quotes in strings and surround with quotes
            return f"'{param_value.replace('\'', '\'\'')}'"
        elif param_value is None:
            return "NULL"
        elif isinstance(param_value, bool):
            return "1" if param_value else "0"
        elif isinstance(param_value, (int, float)):
            # Numbers don't need quotes
            return str(param_value)
        elif isinstance(param_value, (list, tuple)):
            # Handle lists by converting each item
            items = []
            for item in param_value:
                if isinstance(item, str):
                    items.append(f"'{item.replace('\'', '\'\'')}'" )
                elif item is None:
                    items.append("NULL")
                else:
                    items.append(str(item))
            return f"({', '.join(items)})"
        else:
            # Default to string representation
            return f"'{str(param_value)}'"

