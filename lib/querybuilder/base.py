#!/usr/bin/env python3

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseQueryBuilder(ABC):
    """
    Abstract base class for SQL query builders.
    Provides common functionality for all database dialects.
    """
    
    def __init__(self):
        """Initialize the query builder."""
        self.reset()
        
    def reset(self):
        """Reset the query builder state."""
        self._table = ""
        self._select_columns = []
        self._where_conditions = []
        self._order_by = []
        self._group_by = []
        self._limit = None
        self._offset = None
        self._joins = []
        self._parameters = {}
        self._param_counter = 0
    
    @abstractmethod
    def _get_param_placeholder(self, value):
        """Get the appropriate parameter placeholder based on dialect."""
        pass
    
    def table(self, table_name: str) -> 'BaseQueryBuilder':
        """Set the table to query."""
        self._table = table_name
        return self
        
    def select(self, *columns) -> 'BaseQueryBuilder':
        """Add columns to SELECT clause."""
        if not columns:
            self._select_columns = ["*"]
        else:
            self._select_columns = []
            for col in columns:
                if isinstance(col, tuple) and len(col) == 2:
                    # Handle tuple format: ("column_name", "alias")
                    self._select_columns.append(f"{col[0]} AS {col[1]}")
                elif isinstance(col, dict):
                    if "expr" in col:
                        # Handle dictionary format: {"expr": "COUNT(*)", "alias": "total"}
                        if "alias" in col:
                            self._select_columns.append(f"{col['expr']} AS {col['alias']}")
                        else:
                            self._select_columns.append(col["expr"])
                    elif len(col) == 1:
                        # Handle dictionary format: {"total": "COUNT(*)"}
                        alias = list(col.keys())[0]
                        expr = col[alias]
                        self._select_columns.append(f"{expr} AS {alias}")
                    else:
                        # Handle multiple columns in one dict (less common)
                        for alias, expr in col.items():
                            self._select_columns.append(f"{expr} AS {alias}")
                else:
                    # Handle simple string column name
                    self._select_columns.append(str(col))
        return self
    
    def where(self, column: str, operator: str, value: Any) -> 'BaseQueryBuilder':
        """Add a WHERE condition."""
        placeholder = self._get_param_placeholder(value)
        self._where_conditions.append((column, operator, placeholder))
        return self
    
    def where_in(self, column: str, values: List[Any]) -> 'BaseQueryBuilder':
        """Add a WHERE IN condition."""
        if len(values) == 0:
            return self
        
        placeholders = []
        for value in values:
            placeholders.append(self._get_param_placeholder(value))
        
        placeholders_str = ", ".join(placeholders)
        self._where_conditions.append((column, "IN", f"({placeholders_str})"))
        return self
    
    def order_by(self, column: str, direction: str = "ASC") -> 'BaseQueryBuilder':
        """Add ORDER BY clause."""
        self._order_by.append((column, direction.upper()))
        return self
    
    def group_by(self, *columns) -> 'BaseQueryBuilder':
        """Add GROUP BY clause."""
        self._group_by.extend(columns)
        return self
    
    def limit(self, limit: int) -> 'BaseQueryBuilder':
        """Add LIMIT clause."""
        self._limit = limit
        return self
    
    def offset(self, offset: int) -> 'BaseQueryBuilder':
        """Add OFFSET clause."""
        self._offset = offset
        return self
    
    def join(self, table: str, condition: str, join_type: str = "INNER") -> 'BaseQueryBuilder':
        """Add a JOIN clause."""
        self._joins.append((join_type, table, condition))
        return self

    def join_subquery(self, subquery: 'BaseQueryBuilder', alias: str, condition: str, join_type: str = "INNER") -> 'BaseQueryBuilder':
        """Add a JOIN with a subquery."""
        # Get the SQL for the subquery
        subquery_sql, subquery_params = subquery.build()
        
        # Add the subquery parameters to our parameters
        for param_name, param_value in subquery_params.items():
            # Ensure parameter names don't conflict
            new_param_name = f"sub_{param_name}"
            self._parameters[new_param_name] = param_value
            # Replace parameter placeholders in the subquery SQL
            placeholder = self._get_subquery_placeholder(param_name)
            new_placeholder = self._get_subquery_placeholder(new_param_name)
            subquery_sql = subquery_sql.replace(placeholder, new_placeholder)
        
        # Format the subquery with its alias
        subquery_clause = f"({subquery_sql}) AS {alias}"
        
        # Add the join
        self._joins.append((join_type, subquery_clause, condition))
        return self
    
    @abstractmethod
    def _get_subquery_placeholder(self, param_name: str) -> str:
        """Get the placeholder format for subqueries."""
        pass

    def join_where(self, table: str, conditions: List[Tuple[str, str, Any]], join_type: str = "INNER") -> 'BaseQueryBuilder':
        """Add a JOIN with multiple WHERE-style conditions."""
        join_conditions = []
        
        for left, op, right in conditions:
            # Check if right is a primitive value that needs parameterization
            if isinstance(right, (str, int, float, bool)) or right is None:
                placeholder = self._get_param_placeholder(right)
                join_conditions.append(f"{left} {op} {placeholder}")
            else:
                # Assume it's a column reference
                join_conditions.append(f"{left} {op} {right}")
        
        # Join all conditions with AND
        condition_str = " AND ".join(join_conditions)
        
        # Add the join
        self._joins.append((join_type, table, condition_str))
        return self

    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SQL query and parameters."""
        if not self._table:
            raise ValueError("Table name must be specified")
        
        # Build SELECT clause
        select_clause = "SELECT " + ", ".join(self._select_columns)
        
        # Build FROM clause
        from_clause = f"FROM {self._table}"
        
        # Build JOIN clauses
        join_clauses = []
        for join_type, table, condition in self._joins:
            join_clauses.append(f"{join_type} JOIN {table} ON {condition}")
        join_clause = " ".join(join_clauses)
        
        # Build WHERE clause
        where_conditions = []
        for column, operator, placeholder in self._where_conditions:
            where_conditions.append(f"{column} {operator} {placeholder}")
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # Build GROUP BY clause
        group_by_clause = ""
        if self._group_by:
            group_by_clause = "GROUP BY " + ", ".join(self._group_by)
        
        # Build ORDER BY clause
        order_by_clause = ""
        if self._order_by:
            order_parts = [f"{column} {direction}" for column, direction in self._order_by]
            order_by_clause = "ORDER BY " + ", ".join(order_parts)
        
        # Build LIMIT and OFFSET clauses
        limit_clause = self._build_limit_offset_clause()
        
        # Combine all clauses
        query_parts = [
            select_clause,
            from_clause,
            join_clause,
            where_clause,
            group_by_clause,
            order_by_clause,
            limit_clause
        ]
        
        # Filter out empty clauses
        query_parts = [part for part in query_parts if part]
        
        # Join with spaces
        query = " ".join(query_parts)
        
        return query, self._parameters
    
    @abstractmethod
    def _build_limit_offset_clause(self) -> str:
        """Build the LIMIT and OFFSET clause based on dialect."""
        pass

    def get_query(self) -> str:
        """Get the SQL query string."""
        query, _ = self.build()
        return query
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get the query parameters."""
        _, params = self.build()
        return params
    
    @abstractmethod
    def _format_value_for_raw_sql(self, param_value: Any) -> str:
        """Format a value for raw SQL based on dialect."""
        pass

    def get_raw_sql(self) -> str:
        """Get a clean SQL query string with parameters substituted."""
        query, params = self.build()
        
        # Replace each parameter placeholder with its string representation
        clean_sql = query
        for param_name, param_value in params.items():
            placeholder = self._get_subquery_placeholder(param_name)
            formatted_value = self._format_value_for_raw_sql(param_value)
            clean_sql = clean_sql.replace(placeholder, formatted_value)
        
        return clean_sql

