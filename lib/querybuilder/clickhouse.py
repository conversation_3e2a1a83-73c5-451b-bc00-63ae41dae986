#!/usr/bin/env python3

import logging
from typing import Dict, Any,  Tuple
from .base import BaseQueryBuilder

# Configure logging
logger = logging.getLogger(__name__)

class ClickHouseQueryBuilder(BaseQueryBuilder):
    """ClickHouse-specific query builder implementation."""
    
    def _get_param_placeholder(self, value):
        """Get ClickHouse parameter placeholder."""
        self._param_counter += 1
        param_name = f"p{self._param_counter}"
        self._parameters[param_name] = value
        return f"{{{param_name}}}"
    
    def _get_subquery_placeholder(self, param_name: str) -> str:
        """Get ClickHouse subquery placeholder format."""
        return f"{{{param_name}}}"
    
    def _build_limit_offset_clause(self) -> str:
        """Build ClickHouse LIMIT/OFFSET clause."""
        if self._limit is None:
            return ""
        
        if self._offset is not None:
            return f"LIMIT {self._offset}, {self._limit}"
        else:
            return f"LIMIT {self._limit}"
    
    def _format_value_for_raw_sql(self, param_value: Any) -> str:
        """Format a value for ClickHouse raw SQL."""
        if isinstance(param_value, str):
            # Escape single quotes in strings and surround with single quotes
            return f"'{param_value.replace('\'', '\'\'')}'"
        elif param_value is None:
            return "NULL"
        elif isinstance(param_value, bool):
            return "1" if param_value else "0"
        elif isinstance(param_value, (int, float)):
            # Numbers don't need quotes
            return str(param_value)
        elif isinstance(param_value, (list, tuple)):
            # Handle lists by converting each item
            items = []
            for item in param_value:
                if isinstance(item, str):
                    items.append(f"'{item.replace('\'', '\'\'')}'" )
                elif item is None:
                    items.append("NULL")
                else:
                    items.append(str(item))
            return f"({', '.join(items)})"  # ClickHouse uses () for arrays
        else:
            # Default to string representation
            return f"'{str(param_value)}'"
    
    def final(self) -> 'ClickHouseQueryBuilder':
        """
        Add FINAL modifier to the query.
        
        In ClickHouse, the FINAL modifier forces the query to read the 
        most recent version of data parts for tables with the MergeTree engine.
        
        Returns:
            Self for method chaining
        """
        self._final = True
        return self
    
    def sample(self, sample_ratio: float) -> 'ClickHouseQueryBuilder':
        """
        Add SAMPLE modifier to the query.
        
        In ClickHouse, the SAMPLE modifier allows querying only a fraction of data.
        
        Args:
            sample_ratio: Sampling ratio between 0 and 1
            
        Returns:
            Self for method chaining
        """
        if not 0 <= sample_ratio <= 1:
            raise ValueError("Sample ratio must be between 0 and 1")
        
        self._sample = sample_ratio
        return self
    
    def prewhere(self, column: str, operator: str, value: Any) -> 'ClickHouseQueryBuilder':
        """
        Add a PREWHERE condition.
        
        In ClickHouse, PREWHERE is a more efficient version of WHERE that 
        is applied before reading the data from disk.
        
        Args:
            column: Column name
            operator: Comparison operator (=, >, <, etc.)
            value: Value to compare against
            
        Returns:
            Self for method chaining
        """
        placeholder = self._get_param_placeholder(value)
        if not hasattr(self, '_prewhere_conditions'):
            self._prewhere_conditions = []
        self._prewhere_conditions.append((column, operator, placeholder))
        return self
    
    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SQL query and parameters."""
        if not self._table:
            raise ValueError("Table name must be specified")
        
        # Build SELECT clause
        select_clause = "SELECT " + ", ".join(self._select_columns)
        
        # Build FROM clause
        from_clause = f"FROM {self._table}"
        
        # Add FINAL modifier if specified
        if hasattr(self, '_final') and self._final:
            from_clause += " FINAL"
        
        # Add SAMPLE modifier if specified
        if hasattr(self, '_sample') and self._sample is not None:
            from_clause += f" SAMPLE {self._sample}"
        
        # Build JOIN clauses
        join_clauses = []
        for join_type, table, condition in self._joins:
            # ClickHouse uses GLOBAL for distributed queries
            if join_type.upper() == "GLOBAL":
                join_clauses.append(f"GLOBAL JOIN {table} ON {condition}")
            else:
                join_clauses.append(f"{join_type} JOIN {table} ON {condition}")
        join_clause = " ".join(join_clauses)
        
        # Build PREWHERE clause
        prewhere_clause = ""
        if hasattr(self, '_prewhere_conditions') and self._prewhere_conditions:
            prewhere_conditions = []
            for column, operator, placeholder in self._prewhere_conditions:
                prewhere_conditions.append(f"{column} {operator} {placeholder}")
            prewhere_clause = "PREWHERE " + " AND ".join(prewhere_conditions)
        
        # Build WHERE clause
        where_conditions = []
        for column, operator, placeholder in self._where_conditions:
            where_conditions.append(f"{column} {operator} {placeholder}")
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # Build GROUP BY clause
        group_by_clause = ""
        if self._group_by:
            group_by_clause = "GROUP BY " + ", ".join(self._group_by)
        
        # Build ORDER BY clause
        order_by_clause = ""
        if self._order_by:
            order_parts = [f"{column} {direction}" for column, direction in self._order_by]
            order_by_clause = "ORDER BY " + ", ".join(order_parts)
        
        # Build LIMIT and OFFSET clauses
        limit_clause = self._build_limit_offset_clause()
        
        # Combine all clauses
        query_parts = [
            select_clause,
            from_clause,
            join_clause,
            prewhere_clause,
            where_clause,
            group_by_clause,
            order_by_clause,
            limit_clause
        ]
        
        # Filter out empty clauses
        query_parts = [part for part in query_parts if part]
        
        # Join with spaces
        query = " ".join(query_parts)
        
        return query, self._parameters
    
    def with_totals(self) -> 'ClickHouseQueryBuilder':
        """
        Add WITH TOTALS modifier to the query.
        
        In ClickHouse, WITH TOTALS returns an additional row with the total values
        for aggregate functions.
        
        Returns:
            Self for method chaining
        """
        self._with_totals = True
        return self
    
    def settings(self, **kwargs) -> 'ClickHouseQueryBuilder':
        """
        Add SETTINGS to the query.
        
        In ClickHouse, SETTINGS allows specifying query-level settings.
        
        Args:
            **kwargs: Settings as key-value pairs
            
        Returns:
            Self for method chaining
        """
        if not hasattr(self, '_settings'):
            self._settings = {}
        self._settings.update(kwargs)
        return self
    
    def format(self, format_name: str) -> 'ClickHouseQueryBuilder':
        """
        Add FORMAT clause to the query.
        
        In ClickHouse, FORMAT specifies the output format.
        
        Args:
            format_name: Format name (e.g., 'JSON', 'CSV', 'TabSeparated')
            
        Returns:
            Self for method chaining
        """
        self._format = format_name
        return self
    
    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SQL query and parameters."""
        query, params = super().build()
        
        # Add WITH TOTALS if specified
        if hasattr(self, '_with_totals') and self._with_totals and 'GROUP BY' in query:
            # Insert WITH TOTALS after GROUP BY clause
            parts = query.split('GROUP BY')
            group_by_parts = parts[1].split(' ')
            group_by_clause = group_by_parts[0]
            rest = ' '.join(group_by_parts[1:])
            query = f"{parts[0]}GROUP BY {group_by_clause} WITH TOTALS {rest}"
        
        # Add SETTINGS if specified
        if hasattr(self, '_settings') and self._settings:
            settings_parts = []
            for key, value in self._settings.items():
                if isinstance(value, str):
                    settings_parts.append(f"{key} = '{value}'")
                else:
                    settings_parts.append(f"{key} = {value}")
            query += " SETTINGS " + ", ".join(settings_parts)
        
        # Add FORMAT if specified
        if hasattr(self, '_format') and self._format:
            query += f" FORMAT {self._format}"
        
        return query, params

