#!/usr/bin/env python3
"""Command-line interface for MyProject."""
from datetime import datetime
import sys
import logging
import click
from migrations.apply_migration import apply_migration, migrate_all, init_migration
from utils.logging import setup_logging

logger = logging.getLogger(__name__)

logging.basicConfig(level=logging.ERROR)


@click.group()
@click.pass_context
def cli(ctx):
    """MyProject CLI application."""
    ctx.ensure_object(dict)
    # Setup logging
    setup_logging(log_file_name='migration.log')


# python3 -m cli.migration init
@cli.command("init") 
def init():
    return init_migration()
#def


# python3 -m cli.migration migrate --migration migrations/elasticpricedb/001_initial_schema --action=up --dry-run
@cli.command("migrate") 
@click.option("--migration", required=True, type=str,  help="Path to migration file")
@click.option("--action", required=False, type=str, default="up", help="action (up/down)")
@click.option("--dry-run", required=False, is_flag=True, type=bool,  default=False, help="Print SQL without executing")
def migrate(migration: str, action: str, dry_run: bool):
    return apply_migration(migration, action, dry_run)
#def


# python3 -m cli.migration migrate-all --directory migrations/elasticpricedb --action=up --dry-run
@cli.command("migrate-all")
@click.option("--directory", required=True, type=str, help="Directory containing migration files")
@click.option("--action", required=False, type=str, default="up", help="Action (up/down)")
@click.option("--dry-run", required=False, is_flag=True, type=bool, default=False, help="Print SQL without executing")
@click.option("--stop-on-error", required=False, is_flag=True, type=bool, default=True, help="Stop on first error")
@click.option("--amount", required=False, type=int, default=-1, help="Amount of migrations to apply")
def migrate_all_command(directory: str, action: str, dry_run: bool, stop_on_error: bool, amount: int):
    """Run all migrations in a directory in alphabetical order."""
    return migrate_all(directory, action, dry_run, stop_on_error, amount)
#def


def main():
    """Main entry point for the CLI application."""
    try:
        cli(obj={})
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())

