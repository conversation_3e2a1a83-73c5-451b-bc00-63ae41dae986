#!/usr/bin/env python3
"""Command-line interface for MyProject."""
from datetime import datetime
import sys
import logging
import click

from utils.logging import setup_logging
from config import settings

logger = logging.getLogger(__name__)


@click.group()
@click.pass_context
def cli(ctx):
    ctx.ensure_object(dict)    
    # Setup logging
    setup_logging(log_file_name="cli.log", log_level=settings().env('LOG_LEVEL', 'INFO'))


# Machine learning model commands group
@cli.group()
def elastic_price():
    """Machine learning model commands."""
    pass

# python3 -m cli.ml elastic-price train  2>&1|tee ./logs/console_output.log
@elastic_price.command("train")
def train():
    """learn model for model training."""

    import time
    from lib.cron import is_now_between_midnight_and_one
    from datetime import datetime, timezone
    from facades.redis.redisdb import RedisFacade   
    if RedisFacade.instance().db_query(1).exists("cli.ml__elastic-price__train") :
        print('sleep(60)')
        time.sleep(60)
        return

    RedisFacade.instance().db_query(1).set("cli.ml__elastic-price__train", "1", 20*60)

    if not is_now_between_midnight_and_one(datetime.now(timezone.utc)):
        print("The current time is not between midnight and 1 AM.")
        return

    from domains.elasticprice.product_snapshot import ProductSnapshot

    snapshot = ProductSnapshot.new_instance()
    count = snapshot.make_snapshot()
    print('snapshot count=', count)

    if count > 0:
        try:
            from domains.elasticprice.model_ep1 import ElasticPrice1Model
            trainer = ElasticPrice1Model()
            trainer.train()
        except Exception as e:
            print(f"Error training 1 model: {str(e)}")

    try:
        from domains.elasticprice.model_ep2 import ModelEp2Trainer
        trainer = ModelEp2Trainer()
        trainer.train()
    except Exception as e:
        print(f"Error training 2 model: {str(e)}")

#def


# python3 -m cli.ml elastic-price retrain --model=ep2 --new-model=1 --snapshot-days=3 2>&1|tee ./logs/console_output.log
@elastic_price.command("retrain") 
@click.option("--model", required=True, default="ep1", type=str, help="Model name. Options: ep1, ep2")
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=100, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def retrain(model, new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""

    if model == "ep1":
        from domains.elasticprice.model_ep1 import ElasticPrice1Model
        trainer = ElasticPrice1Model()
        trainer.train(force_new_model=bool(new_model), snapshot_days=snapshot_days, data_bulk_size=data_bulk_size)
    elif model == "ep2":
        from domains.elasticprice.model_ep2 import ModelEp2Trainer
        trainer = ModelEp2Trainer()
        trainer.train(force_new_model=bool(new_model), snapshot_days=snapshot_days, data_bulk_size=data_bulk_size)
    else:
        print("Invalid model name")
#def


# python3 -m cli.ml elastic-price test-train --new-model=1 --snapshot-days=3 2>&1|tee ./logs/test_train_output.log
@elastic_price.command("test-train") 
@click.option("--new-model", required=False, default=0, type=int, help="Force new model")
@click.option("--snapshot-days", required=False, default=2, type=int, help="Number of days to snapshot")
@click.option("--data-bulk-size", required=False, default=1000000, type=int, help="Number of data points to load in bulk")
def test_train(new_model, snapshot_days, data_bulk_size):
    """learn model for model training."""


    from domains.elasticprice.model_ep2 import ModelEp2Trainer #, load_last_trained_ep3_model, predict, result_from_predicted
    trainer = ModelEp2Trainer()
    trainer.train(force_new_model=True, snapshot_days=3)
    # trainer.train_from_csv('train_data_ep3_20250624134405.csv', force_new_model=True, save_to_file_only=True)
#def




def main():
    """Main entry point for the CLI application."""
    try:
        cli(obj={})
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())


# # If installed as a package
# myproject learn load-data --customer-id=19

# # If running directly from the script
# python -m cli.main learn load-data --customer-id=19
