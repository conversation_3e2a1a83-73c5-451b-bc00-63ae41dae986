# """User management commands for the CLI."""
# import logging
# from typing import Optional

# from app.core.models import User
# from app.db.repositories.user_repository import UserRepository

# logger = logging.getLogger(__name__)


# def list_users(active_only: bool = False) -> None:
#     """
#     List all users in the system.
    
#     Args:
#         active_only: If True, only show active users
#     """
#     try:
#         repo = UserRepository()
#         users = repo.get_all(active_only=active_only)
        
#         if not users:
#             print("No users found.")
#             return
        
#         # Print header
#         print(f"{'ID':<5} {'Username':<20} {'Email':<30} {'Status':<10}")
#         print("-" * 70)
        
#         # Print users
#         for user in users:
#             status = "Active" if user.is_active else "Inactive"
#             print(f"{user.id:<5} {user.username:<20} {user.email:<30} {status:<10}")
            
#         print(f"\nTotal: {len(users)} users")
        
#     except Exception as e:
#         logger.error(f"Error listing users: {e}")
#         print(f"Error: {e}")


# def create_user(username: str, email: str) -> Optional[User]:
#     """
#     Create a new user.
    
#     Args:
#         username: Username for the new user
#         email: Email address for the new user
        
#     Returns:
#         The created User object or None if creation failed
#     """
#     try:
#         repo = UserRepository()
        
#         # Check if user already exists
#         existing_user = repo.get_by_username(username)
#         if existing_user:
#             print(f"Error: User with username '{username}' already exists.")
#             return None
        
#         # Create new user
#         user = User(username=username, email=email)
#         created_user = repo.create(user)
        
#         print(f"User created successfully:")
#         print(f"ID: {created_user.id}")
#         print(f"Username: {created_user.username}")
#         print(f"Email: {created_user.email}")
        
#         return created_user
        
#     except Exception as e:
#         logger.error(f"Error creating user: {e}")
#         print(f"Error: {e}")
#         return None