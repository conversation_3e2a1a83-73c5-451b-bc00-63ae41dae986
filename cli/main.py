#!/usr/bin/env python3
"""Command-line interface for MyProject."""
import sys
import logging
import click
import os
import shutil

from config import settings
from utils.logging import setup_logging

logger = logging.getLogger(__name__)

# Load environment variables automatically in your CLI
# python3 -m cli.main --env-file=.env.production
# python3 -m cli.main env show

# Export environment variables to your shell:
# # This will show instructions
# python3 -m cli.main env export
# # This will generate a script to be evaluated
# eval $(python3 -m cli.main env export-script)

# # Use in your code
# from utils.env import export_env_to_os
# # At the start of your application
# export_env_to_os()

@click.group()
@click.pass_context
def cli(ctx):
    ctx.ensure_object(dict)    
    # Setup logging
    setup_logging(log_file_name="cli.log", log_level=settings().env('LOG_LEVEL', 'INFO'))


# # Clean cache in current directory
# python3 -m cli.main clean-cache

# # Clean cache in specific directory
# python3 -m cli.main clean-cache --directory ./myproject

# # Or with short option
# python3 -m cli.main clean-cache -d ./

@cli.command("clean-cache")
@click.option("--directory", "-d", default=".", help="Directory to clean (default: current directory)", show_default=True)
def clean_pycache(directory):
    """Clean Python cache files (__pycache__ directories and .pyc files)."""
    count = 0
    for root, dirs, files in os.walk(directory):
        # Remove __pycache__ directories
        if "__pycache__" in dirs:
            cache_dir = os.path.join(root, "__pycache__")
            click.echo(f"Removing {cache_dir}")
            shutil.rmtree(cache_dir)
            count += 1
            
        # Remove .pyc files
        for file in files:
            if file.endswith(".pyc"):
                pyc_file = os.path.join(root, file)
                click.echo(f"Removing {pyc_file}")
                os.remove(pyc_file)
                count += 1
    
    click.echo(f"Cleaned {count} cache files/directories")


# python3 -m cli.main app-was-deployed
@cli.command("app-was-deployed")
def app_was_deployed():
    try:
        from facades.redis.redisdb import RedisFacade
        RedisFacade.instance().db_query(1).clear()
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1



# python3 -m cli.main environment --name=stage
@click.option("--name", default="stage", help="environment  name (options: stage, rc, production)", show_default=True) 
@cli.command("environment")
def environment(name):
    """Set environment variables for the application."""

    env_file = None
    if name == "stage":
        env_file = ".env.stage"
    elif name == "rc":
        env_file = ".env.rc"
    elif name == "production":
        env_file = ".env.production"
    else:
        raise click.ClickException("Invalid environment name")
    
    logger.info(f"Setting environment to {name}")

    if not os.path.exists(env_file):
        raise click.ClickException(f"Environment file {env_file} not found")

    #copy env file to .env
    e1 = open(env_file, "r")
    e2 = open(".env", "w")
    e2.write(e1.read())
    e1.close()
    e2.close()

    logger.info(f"Environment file {env_file} copied to .env")
#def



def main():
    """Main entry point for the CLI application."""
    try:
        cli(obj={})
        return 0
    except Exception as e:
        logger.error(f"Error executing command: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())

