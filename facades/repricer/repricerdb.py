#!/usr/bin/env python3

import os
import logging
import pymysql
from typing import Dict, Optional, List, Any
from db.mysql.mysqldb import MysqlDbClient
from db.mysql.query import Query
from lib.singleton import SingletonDoubleChecked
from config import settings

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RepricerDbFacade(SingletonDoubleChecked):
    """
    Facade for managing database connections to the Repricer database system.
    Implements connection pooling, error handling, and automatic reconnection.
    """

    def __init__(self):
        config = settings()
        # Main database configuration
        self._DATABASE_MAIN_HOST = config.env('DATABASE_MAIN_HOST', 'localhost')
        self._DATABASE_MAIN_PORT = int(config.env('DATABASE_MAIN_PORT', '3306'))
        self._DATABASE_MAIN_DBNAME = config.env('DATABASE_MAIN_DBNAME', 'main_db')
        self._DATABASE_MAIN_USER = config.env('DATABASE_MAIN_USER', 'user')
        self._DATABASE_MAIN_PASSWORD = config.env('DATABASE_MAIN_PASSWORD', 'password')

        # Client database configuration
        self._DATABASE_CLIENT_HOST = config.env('DATABASE_CLIENT_HOST', 'localhost,localhost').split(',')
        self._DATABASE_CLIENT_PORT = int(config.env('DATABASE_CLIENT_PORT', '3306'))
        self._DATABASE_CLIENT_DBNAME = config.env('DATABASE_CLIENT_DBNAME', 'client_db_')
        self._DATABASE_CLIENT_USER = config.env('DATABASE_CLIENT_USER', 'user')
        self._DATABASE_CLIENT_PASSWORD = config.env('DATABASE_CLIENT_PASSWORD', 'password')

        # Connection pool settings
        self._MAX_DB_CONNECTIONS = int(config.env('MAX_DB_CONNECTIONS', '20'))
        self._CONNECTION_TIMEOUT = int(config.env('DB_CONNECTION_TIMEOUT', '60'))

        self._mysqlClient = MysqlDbClient(max_db_connections=self._MAX_DB_CONNECTIONS, connection_timeout=self._CONNECTION_TIMEOUT)
        self.__customers: Dict[str, Dict] = {}
        # logger.info("RepricerDbFacade initialized")
    #def

    def __del__(self):
        self._mysqlClient.close_all_connections()
        self._mysqlClient = None
    #def


    def main_db(self) -> Optional[pymysql.Connection]:
        """Get a connection to the main database."""

        try:
            return self._mysqlClient.get_connection(self._DATABASE_MAIN_DBNAME,
                self._DATABASE_MAIN_HOST,
                self._DATABASE_MAIN_PORT,
                self._DATABASE_MAIN_USER,
                self._DATABASE_MAIN_PASSWORD
            )
        except ConnectionError as e:
            logger.error(f"Failed to connect to main database: {str(e)}")
            return None
    #def
    
    def main_db_query(self) -> Query:
        """Get a connection to the main database."""
        return Query(self.main_db())
    #def

    def _get_customer(self, customer_id: int) -> Optional[Dict]:
        """
        Get customer information from cache or database.
        
        Args:
            customer_id: The customer ID to look up
            
        Returns:
            Customer information dictionary or None if not found
        """
        key = f"customer{customer_id}"
        
        # Return from cache if available
        if key in self.__customers:
            return self.__customers[key]
        
        try:
            connection = self.main_db()
            with connection.cursor() as cursor:
                # Use parameterized query to prevent SQL injection
                sql = "SELECT id, db_index FROM `customer` WHERE `id`=%s"
                cursor.execute(sql, (customer_id,))
                result = cursor.fetchone()
                
                if result:
                    self.__customers[key] = result
                    return result
                logger.warning(f"Customer ID {customer_id} not found in database")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving customer {customer_id}: {str(e)}")
            return None
    #def

    def client_db(self, customer_id: int) -> Optional[pymysql.Connection]:
        """
        Get a connection to a client database based on customer ID.
        
        Args:
            customer_id: The customer ID to connect to
            
        Returns:
            Database connection or None if connection failed
        """
        
        # Get customer info to determine which host to use
        customer = self._get_customer(customer_id)
        if not customer:
            logger.error(f"Cannot connect to client DB: Customer {customer_id} not found")
            return None
        
        dbname = f"{self._DATABASE_CLIENT_DBNAME}{customer_id}"

        # Determine which host to use based on db_index
        db_index = int(customer['db_index'])
        host = self._DATABASE_CLIENT_HOST[db_index] if db_index < len(self._DATABASE_CLIENT_HOST) else self._DATABASE_CLIENT_HOST[0]
        
        try:
            # Create a new connection
            return self._mysqlClient.get_connection(
                dbname,
                host,
                self._DATABASE_CLIENT_PORT,
                self._DATABASE_CLIENT_USER,
                self._DATABASE_CLIENT_PASSWORD
            )
        except ConnectionError as e:
            logger.error(f"Failed to connect to client database {dbname} on {host}: {str(e)}")
            return None
    #def
            

    def client_db_query(self, customer_id: int) -> Query:
        """Get a connection to the main database."""
        return Query(self.client_db(customer_id))
    #def