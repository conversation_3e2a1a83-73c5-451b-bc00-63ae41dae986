#!/usr/bin/env python3

import logging
import json
import os
from typing import Any, Dict, Optional, List, Callable
import redis
from functools import wraps
from db.redis.query import Query
from db.redis.redisdb import RedisClient
from lib.singleton import SingletonDoubleChecked
from decimal import Decimal
from config import settings

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JsonEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles Decimal objects."""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(JsonEncoder, self).default(obj)

class RedisFacade(SingletonDoubleChecked):
    """
    Facade for interacting with Redis cache.
    Handles connection management, data serialization, and common cache operations.
    """
    
    def __init__(self):
        self._REDIS_HOST = settings().env('REDIS_HOST', 'localhost')
        self._REDIS_PORT = int(settings().env('REDIS_PORT', '6379'))
        self._REDIS_DB = int(settings().env('REDIS_DB', '0'))
        self._REDIS_PASSWORD = settings().env('REDIS_PASSWORD', None)


        """Initialize the Redis cache facade."""
        self._redis_client = RedisClient()
        
    def __del__(self):
        """Clean up connections when object is destroyed."""
        self._redis_client.close_all_connections()
        self._redis_client = None

    def db(self, db: int = 0) -> Optional[redis.Redis]:
        return self._redis_client.client(db, self._REDIS_HOST, self._REDIS_PORT, self._REDIS_PASSWORD)
    
    def db_query(self, db: int = 0) -> Query:
        return Query(self.db(db))



def cached(db: int, key_pattern: str, expiry: Optional[int] = None):
    """
    Decorator for caching function results in Redis.
    
    Args:
        key_pattern: Pattern for cache key with {} placeholders for args/kwargs
        expiry: Optional expiration time in seconds
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get cache facade
            cache = RedisFacade.instance().db(db)
            
            # Format key with args and kwargs
            all_args = list(args)
            all_kwargs = kwargs.copy()
            
            # Add self to kwargs if it's a method
            if args and hasattr(args[0], '__class__'):
                all_kwargs['self_class'] = args[0].__class__.__name__
                all_args = all_args[1:]
            
            # Create cache key
            try:
                cache_key = key_pattern.format(*all_args, **all_kwargs)
            except (IndexError, KeyError):
                logger.warning(f"Could not format cache key pattern: {key_pattern}")
                return func(*args, **kwargs)
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, expiry)
            logger.debug(f"Cached result for key: {cache_key}")
            
            return result
        return wrapper
    return decorator
