#!/usr/bin/env python3

import psycopg
from typing import Dict, Optional, List, Any
from db.pg.pgdb import PostgresDbClient
from db.pg.query import Query
from db.pg.transaction import Transaction
from lib.singleton import SingletonDoubleChecked

from config import settings

# Configure logging
# # logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)


class ElasticPriceDbFacade(SingletonDoubleChecked):
    """
    Facade for managing database connections to the ElasticPrice PostgreSQL database.
    Implements connection pooling, error handling, and automatic reconnection.
    """

    def __init__(self):
        self._ELASTIC_PRICE_DB_HOST = settings().env('ELASTIC_PRICE_DB_HOST', 'localhost')
        self._ELASTIC_PRICE_DB_PORT = int(settings().env('ELASTIC_PRICE_DB_PORT', '5432'))
        self._ELASTIC_PRICE_DB_DATABASE = settings().env('ELASTIC_PRICE_DB_DATABASE', 'reports')
        self._ELASTIC_PRICE_DB_USERNAME = settings().env('ELASTIC_PRICE_DB_USERNAME', 'user')
        self._ELASTIC_PRICE_DB_PASSWORD = settings().env('ELASTIC_PRICE_DB_PASSWORD', 'password')

        # Connection pool settings
        self._MAX_DB_CONNECTIONS = int(settings().env('MAX_DB_CONNECTIONS', '20'))
        self._CONNECTION_TIMEOUT = int(settings().env('CONNECTION_TIMEOUT', '60'))

        self._pgDbClient = PostgresDbClient(max_db_connections=self._MAX_DB_CONNECTIONS, connection_timeout=self._CONNECTION_TIMEOUT)
        # logger.info("ElasticPriceDbFacade initialized")

    def __del__(self):
        self._pgDbClient.close_all_connections()
        self._pgDbClient = None

    def db(self) -> Optional[psycopg.Connection]:
        """Get a connection to the price database."""
        return self._pgDbClient.get_connection(
            self._ELASTIC_PRICE_DB_DATABASE,
            self._ELASTIC_PRICE_DB_HOST,
            self._ELASTIC_PRICE_DB_PORT,
            self._ELASTIC_PRICE_DB_USERNAME,
            self._ELASTIC_PRICE_DB_PASSWORD
        )

    def db_query(self) -> Query:
        return Query(self.db())
    
    def db_transaction(self) -> Transaction:
        return Transaction(self.db())
    
#class
