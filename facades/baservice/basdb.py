#!/usr/bin/env python3

import logging
import json
from typing import Any, Optional
from db.clickhouse.db import ClickHouseDbClient
from db.clickhouse.query import Query
from lib.singleton import SingletonDoubleChecked
from config import settings

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BasDbFacade(SingletonDoubleChecked):
    """
    Facade for managing ClickHouse database connections.
    Implements connection pooling, error handling, and automatic reconnection.
    """

    def __init__(self):
        self._CLICKHOUSE_HOST = settings().env('CLICKHOUSE_HOST', 'localhost')
        self._CLICKHOUSE_PORT = settings().env('CLICKHOUSE_PORT', '8123')
        self._CLICKHOUSE_DB_NAME = settings().env('CLICKHOUSE_DB_NAME', 'default')
        self._CLICKHOUSE_USERNAME = settings().env('CLICKHOUSE_USERNAME', 'clickhouse')
        self._CLICKHOUSE_PASSWORD = settings().env('CLICKHOUSE_PASSWORD', 'password')
        self._CLICKHOUSE_CLUSTER_NAME = settings().env('CLICKHOUSE_CLUSTER_NAME', 'cluster_sl')
        self._CLICKHOUSE_MASTER_NODES = settings().env(
            'CLICKHOUSE_MASTER_NODES', 
            'localhost,localhost,localhost,localhost'
        ).split(',')
        self._CLICKHOUSE_MASTER_NODES_MAP = json.loads(settings().env('CLICKHOUSE_MASTER_NODES_MAP', '[[0, 1], [2, 3]]'))

        """Initialize the connection pool."""
        self._clickhouse_client = ClickHouseDbClient(max_db_connections = 20, connection_timeout = 60, client_name='elastic_price_app')
    #def

    def __del__(self):
        """Clean up connections when object is destroyed."""
        self._clickhouse_client.close_all_connections()
        self._clickhouse_client = None
    #def

    def client_db(self, customer_id: int) -> Optional[Any]:
        """
        Get a connection to the appropriate ClickHouse node based on customer ID.
        
        Args:
            customer_id: The customer ID to determine which node to connect to
            
        Returns:
            ClickHouse client connection with cluster context
        """
        # Determine which node to use based on customer_id
        node_index = customer_id % len(self._CLICKHOUSE_MASTER_NODES_MAP)
        nodes = self._CLICKHOUSE_MASTER_NODES_MAP[node_index]
        host = self._CLICKHOUSE_MASTER_NODES[nodes[0]]
        
        try:
            return self._clickhouse_client.get_connection(
                host,
                self._CLICKHOUSE_PORT,
                self._CLICKHOUSE_USERNAME,
                self._CLICKHOUSE_PASSWORD,
                self._CLICKHOUSE_DB_NAME,
            )
        except Exception as e:
            logger.error(f"Failed to connect to ClickHouse on {host}: {str(e)}")
            return None
    #def
        

    def client_db_query(self, customer_id: int) -> Query:
        return Query(self.client_db(customer_id))
    #def


def table_name(customer_id: int, name: str) -> str:
    """
    Generate a table name with customer prefix.
    
    Args:
        customer_id: Customer ID
        name: Base table name
        
    Returns:
        Full table name with customer prefix
    """
    c = "00000" + str(customer_id)
    return f"customer_{c[-5:]}.{name}"
#def
