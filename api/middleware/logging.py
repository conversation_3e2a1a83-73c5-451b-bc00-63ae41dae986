"""Logging middleware for FastAPI."""
import time
import logging
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    def __init__(self, app: ASGIApp):
        """Initialize the middleware."""
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """
        Process the request, log details, and pass it to the next middleware/route handler.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware/route handler
            
        Returns:
            The HTTP response
        """
        # Generate a unique request ID
        request_id = request.headers.get("X-Request-ID", f"req-{time.time()}")
        
        # Log the request
        logger.info(
            f"Request {request_id}: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        # Record request start time
        start_time = time.time()
        
        # Process the request and get the response
        try:
            response = await call_next(request)
            
            # Calculate request duration
            process_time = time.time() - start_time
            
            # Log the response
            logger.info(
                f"Response {request_id}: {response.status_code} "
                f"processed in {process_time:.4f}s"
            )
            
            # Add custom headers to the response
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Log any exceptions that occur during request processing
            process_time = time.time() - start_time
            logger.error(
                f"Error {request_id}: {str(e)} "
                f"after {process_time:.4f}s"
            )
            raise