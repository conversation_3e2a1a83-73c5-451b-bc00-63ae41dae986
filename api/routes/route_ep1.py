import logging
from fastapi import APIRouter, Response
from typing import Any, List, Optional
import json
from pydantic import BaseModel, Field

from domains.elasticprice.model import ElasticPriceNN
from domains.elasticprice.model_data import result_from_predicted

from domains.elasticprice.model_ep1 import ElasticPrice1Model
from domains.repository.eprice_repository import ElasticPriceRepository
from facades.redis.redisdb import RedisFacade
from lib.json import json_decode, json_encode


logger = logging.getLogger(__name__)

# Custom response class for formatted JSON
class PrettyJSONResponse(Response):
    media_type = "application/json"

    def render(self, content: Any) -> bytes:
        return json.dumps(content, indent=2).encode("utf-8")

# Request DTO
class PredictPriceRequest(BaseModel):
    title: str = Field(..., description="Product title")
    marketplace_id: str = Field(..., description="Marketplace identifier")
    quantity: int = Field(1, description="Quantity of items")
    stock: int = Field(0, description="Current stock level")
    item_price: float = Field(0.0, description="Current item price")

# Parameter DTO for each optimization parameter set
class OptimizationParams(BaseModel):
    step_price_type: str = Field(..., description="Price step type (UP/DOWN)")
    limit_type: str = Field(..., description="Limit type (AFTER/BEFORE)")
    step_price: float = Field(..., description="Price step value")
    step_point: str = Field(..., description="Step point type (VALUE/PERCENT)")
    limit: int = Field(..., description="Limit value")
    limit_day: int = Field(..., description="Limit day")
    type: str = Field(..., description="Type (ORDER/DAY)")

# Response DTO
class PredictPriceResponse(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    optimization: List[OptimizationParams] = Field(..., description="Optimization parameters")
    # optimization_predicted: List[Dict[str, float]] = Field(..., description="Predicted optimization parameters")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Request DTO for batch predictions
class BatchItemRequest(BaseModel):
    request_id: str = Field(..., description="Request ID")
    params: PredictPriceRequest = Field(..., description="List of products to predict pricing for")

class PredictPriceBatchRequest(BaseModel):
    items: List[BatchItemRequest] = Field(..., description="List of products to predict pricing for")

class BatchItemResult(BaseModel):
    success: bool = Field(True, description="Request success status")
    request_id: str = Field(..., description="Request ID")
    optimization: List[OptimizationParams] = Field(..., description="Optimization parameters")
    # optimization_predicted: List[Dict[str, float]] = Field(..., description="Predicted optimization parameters")


# Response DTO for batch predictions
class PredictPriceBatchResponse(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[str] = Field(None, description="Overall error message")
    results: List[BatchItemResult] = Field(..., description="Individual prediction results")
    model_info: Optional[dict] = Field(None, description="Model Info")

# Create router for elastic price endpoints
elastic_price_router = APIRouter(tags=["elastic-price"])
_elastic_price_model_cache = {}


def load_last_trained_model() -> tuple[Optional[ElasticPriceNN], dict]:
    cache = RedisFacade.instance().db_query(1)
    global _elastic_price_model_cache

    model = None
    last_trained_model = None
    if cache.exists('last_trained_model'):

        last_trained_model = json_decode(cache.get('last_trained_model'))

        id = None
        if isinstance(last_trained_model, dict):  
            id = last_trained_model.get('id') if last_trained_model.get('id') else None
        
        if id is not None and id in _elastic_price_model_cache:
            model = _elastic_price_model_cache[id]
        else:
            _elastic_price_model_cache = {}
    
    if not model:
        last_model = ElasticPriceRepository.new_instance().get_last_active_trained_model('ep1')

        if last_model is not None and last_model.model_dump is not None:
            _elastic_price_model_cache[last_model.id] = ElasticPriceNN.load_model_dump(last_model.model_dump)

            last_trained_model = {'id' : last_model.id, 'name' : last_model.model_name, 'accuracy' : last_model.accuracy, 'created_at' : last_model.created_at}
            cache.set('last_trained_model', json_encode(last_trained_model), 60)

            model = _elastic_price_model_cache[last_model.id]
     
    return model, last_trained_model
#def


@elastic_price_router.post(
    "/predict", 
    response_class=PrettyJSONResponse, 
    response_model=PredictPriceResponse,
    summary="Predict elastic pricing parameters",
    description="""
    Predicts optimal pricing parameters based on product information.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the title, 
    marketplace, quantity, stock level, and current price to suggest optimal 
    pricing strategies.
    """,
    responses={
        200: {
            "description": "Successful prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "optimization": [
                            {
                                "step_price_type": "UP",
                                "limit_type": "AFTER",
                                "step_price": 5.0,
                                "step_point": "VALUE",
                                "limit": 100,
                                "limit_day": 7,
                                "type": "ORDER"
                            },
                            {
                                "step_price_type": "DOWN",
                                "limit_type": "BEFORE",
                                "step_price": 10.0,
                                "step_point": "PERCENT",
                                "limit": 50,
                                "limit_day": 3,
                                "type": "DAY"
                            }
                        ]
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "title"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to predict price",
                        "model_info": {},
                        "optimization": []
                    }
                }
            }
        }
    }
)
async def predict_price(request: PredictPriceRequest):
    """
    Predict optimal pricing parameters for a product.
    
    The endpoint accepts product details and returns a list of pricing optimization 
    parameters that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - limit_type: Whether the limit applies before or after the price change
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    ai_model, model_info = load_last_trained_model()

    if not ai_model:
        return PredictPriceResponse(success=False, error="Failed to load model", model_info=None, optimization=[])

    x_features = ElasticPrice1Model.build_x_feature(
        request.title, 
        request.marketplace_id, 
        request.quantity, 
        request.stock, 
        request.item_price
    )

    predicted = ai_model.predict(x_features)

    if predicted is None:
        return PredictPriceResponse(success=False, error="Failed to predict price", model_info=model_info, optimization=[])
    
    optimization, optimization_predicted = result_from_predicted(predicted[0])


    # Return the PredictPriceResponse object instead of the raw result
    return PredictPriceResponse(
        success=True, error="", model_info=model_info, 
        optimization=optimization,
        # optimization_predicted=optimization_predicted
    )
#def

@elastic_price_router.post(
    "/predict-batch", 
    response_class=PrettyJSONResponse, 
    response_model=PredictPriceBatchResponse,
    summary="Predict elastic pricing parameters for multiple products",
    description="""
    Predicts optimal pricing parameters for multiple products in a single request.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the title, 
    marketplace, quantity, stock level, and current price to suggest optimal 
    pricing strategies for each product in the batch.
    """,
    responses={
        200: {
            "description": "Successful batch prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "model_info": {"id": 1, "created_at": "2023-06-15T10:30:00"},
                        "results": [
                            {
                                "request_id": "1", 
                                "optimization": [
                                    {
                                        "step_price_type": "UP",
                                        "limit_type": "AFTER",
                                        "step_price": 5.0,
                                        "step_point": "VALUE",
                                        "limit": 100,
                                        "limit_day": 7,
                                        "type": "ORDER"
                                    }
                                ]
                            },
                            {
                                "request_id": "2",
                                "optimization": [
                                    {
                                        "step_price_type": "DOWN",
                                        "limit_type": "BEFORE",
                                        "step_price": 10.0,
                                        "step_point": "PERCENT",
                                        "limit": 50,
                                        "limit_day": 3,
                                        "type": "DAY"
                                    }
                                ]
                            }
                        ]
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "items", 0, "title"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to process batch prediction",
                        "model_info": None,
                        "results": []
                    }
                }
            }
        }
    }
)
async def predict_price_batch(request: PredictPriceBatchRequest):
    """
    Predict optimal pricing parameters for multiple products in a batch.
    
    The endpoint accepts a list of product details and returns a list of pricing optimization 
    parameters for each product that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - limit_type: Whether the limit applies before or after the price change
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    - limit: The limit value
    - limit_day: The limit day
    - type: The type (ORDER/DAY)
    """
    # Load model once for all predictions
    ai_model, model_info = load_last_trained_model()
    
    if not ai_model:
        return PredictPriceBatchResponse(
            success=False, 
            error="Failed to load model", 
            model_info=None, 
            results=[]
        )
    
    # Process each request in the batch
    results = []
    for item in request.items:
        try:
            x_features = ElasticPrice1Model.build_x_feature(
                item.params.title, 
                item.params.marketplace_id, 
                item.params.quantity, 
                item.params.stock, 
                item.params.item_price
            )
            
            predicted = ai_model.predict(x_features)
            
            if predicted is None:
                results.append(
                    BatchItemResult(
                        success=False, 
                        error="Failed to predict price", 
                        request_id=item.request_id,
                        optimization=None
                    )
                )
                continue
            
            optimization, _ = result_from_predicted(predicted[0])
            
            results.append(
                BatchItemResult(
                    success=True, 
                    error=None, 
                    request_id=item.request_id,
                    optimization=optimization
                )
            )
        except Exception as e:
            logger.error(f"Error processing batch item: {str(e)}")
            results.append(
                PredictPriceResponse(
                    success=False, 
                    error=f"Error processing request: {str(e)}", 
                    model_info=model_info, 
                    optimization=[]
                )
            )
    

    # Return the batch response
    return PredictPriceBatchResponse(
        success=True,
        error=None,
        model_info=model_info,
        results=results
    )
#def

