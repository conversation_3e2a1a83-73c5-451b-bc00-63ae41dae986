from setuptools import setup, find_packages


setup(
    name="elastic_price",
    version="0.1.2",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        "fastapi[standard]>=0.115.12",
        "uvicorn>=0.34.2",
        "gunicorn>=21.2.0",  # Add Gunicorn for production
        "pydantic>=2.11.3",
        "pydantic-settings>=2.9.1",
        "python-dotenv>=1.1.0",
        "click>=8.1.8",

        "pandas>=2.2.3",
        "numpy>=2.2.5",
        "scikit-learn>=1.6.1",
        "seaborn>=0.13.2",
        "matplotlib>=3.10.1",
        "scipy>=1.15.2",
        "statsmodels>=0.14.4",
        'torch>=2.7.0', 
        'torchvision>=0.22.0', 
        'torchaudio>=0.22.0',

        "clickhouse-connect>=0.8.17",
        "PyMySQL>=1.1.1",
        "redis>=5.2.1",
        "psycopg[binary]>=3.2.6",
    ],
    dependency_links=[
        'https://download.pytorch.org/whl/cu128'
    ],
    extras_require={
        "dev": [
            # "pytest>=7.4.0",
            # "black>=24.1.0",
            # "isort>=5.12.0",
            # "mypy>=1.5.1",
            # "flake8>=6.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "elastic-price=cli.main:main",
        ],
    },
    python_requires=">=3.12.3",
    author="Vitalii Tykhoniuk",
    author_email="<EMAIL>",
    description="Elastic pricing model for dynamic price optimization",
    keywords="api, cli, python, pricing, optimization, machine learning",
    url="https://gitlab.sl.local/sellerlogic/elastic-price",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3.12",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
)


# pip3 install --no-cache-dir setuptools wheel

# 1. **Build the distribution**:
   
#    python3 setup.py sdist bdist_wheel
   
#    - sdist creates a source distribution (a tarball with your files).
#    - bdist_wheel builds a wheel, which is a binary distribution.
# 2. **Install the package locally** to test:
   
#    pip install .
   
# 3. **Test your package** by importing it in a Python environment or running any scripts you've defined.
# ### Step 4: Publish (Optional)
# To publish your package to PyPI, you'll need a PyPI account. You can use twine to upload your package:
# 1. Install twine if you haven't already:
   
#    pip install twine
   
# 2. Upload the package:
   
#    twine upload dist/*
