#!/usr/bin/env python3

import os
import sys
import argparse
import logging
from pathlib import Path


logger = logging.getLogger(__name__)


def execute_pg_migration(db_facade, sql_statements: list[str], dry_run: bool = False) -> bool:
    """Execute the migration to create the initial ElasticPriceDB schema."""
     # Configure logging
    
    
    if dry_run:
        logger.info("Starting migration without executing")
        # Print SQL statements without executing
        for statement in sql_statements:
            print(statement)
            print("\n")
        return True
    
    logger.info("Starting migration execution")
    conn = db_facade.db()
    try:
        with conn.cursor() as cur:
            for statement in sql_statements:
                logger.info(f"Executing: {statement}...")
                cur.execute(statement)
        
        conn.commit()
        logger.info("Migration successfully applied")
        return True
    
    except Exception as e:
        conn.rollback()
        logger.error(f"Migration failed: {e}", exc_info=True)
        return False
