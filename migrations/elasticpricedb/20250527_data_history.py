#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250527_data_history --action=up --dry-run

def up(dry_run=False)->bool:
        
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.data_train_history",
        """
        CREATE TABLE elastic_price.data_train_history (
            id bigserial NOT NULL,
            customer_id bigint NULL,
            order_marketplace_id varchar NULL,
            asin varchar NULL,
            order_purchase_date date NULL,
            version integer NULL,
            data_json jsonb NULL,
            CONSTRAINT data_train_history_pk PRIMARY KEY (id)
        );
        """,
        "CREATE INDEX data_train_history_order_purchase_date_idx ON elastic_price.data_train_history (order_purchase_date)",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)



def down(dry_run=False)->bool:    
    print("Migration DOWN not implemented yet")
    return True


