#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250528_optimization_history --action=up --dry-run

def up(dry_run=False)->bool:
        
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.product_optimization_snapshot",
        """
        
        CREATE TABLE elastic_price.product_optimization_snapshot (
            id bigserial NOT NULL,
            customer_id bigint NULL,
            snapshot_date_start timestamptz NULL,
            snapshot_date_end timestamptz NULL,
            amount int NULL,
            data_json jsonb NULL,
            CONSTRAINT product_optimization_snapshot_pk PRIMARY KEY (id)
        )
        """,
        "CREATE INDEX product_optimization_snapshot_snapshot_date_start_idx ON elastic_price.product_optimization_snapshot (snapshot_date_start)",
        "CREATE INDEX product_optimization_snapshot_snapshot_date_end_idx ON elastic_price.product_optimization_snapshot (snapshot_date_end)",
        "CREATE INDEX product_optimization_snapshot_amount_idx ON elastic_price.product_optimization_snapshot (amount)",
        "CREATE INDEX product_optimization_snapshot_customer_id_idx ON elastic_price.product_optimization_snapshot (customer_id)",
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:    
    print("Migration DOWN not implemented yet")
    return True



