#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration


# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/20250606_trained_model --action=up --dry-run

def up(dry_run=False)->bool:
        
    sql_statements = [
        "DROP TABLE IF EXISTS elastic_price.trained_model_new",
        """
        CREATE TABLE elastic_price.trained_model_new (
            id serial8 PRIMARY KEY,
            model_name VARCHAR(100),
            active BOOLEAN DEFAULT TRUE,
            loss NUMERIC(10, 6),
            accuracy NUMERIC(10, 6),
            model_dump BYTEA,
            training_data_size INTEGER,
            validation_data_size INTEGER,
            start_time TIMESTAMP WITH TIME ZONE,
            end_time TIMESTAMP WITH TIME ZONE, 
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,

        "INSERT INTO elastic_price.trained_model_new (model_name, active, loss, accuracy, model_dump, training_data_size, validation_data_size, start_time, end_time, created_at) "
        "SELECT 'ep1', active, loss, accuracy, model_dump, training_data_size, validation_data_size, start_time, end_time, created_at FROM elastic_price.trained_model",

        "DROP TABLE IF EXISTS elastic_price.trained_model",
        "ALTER TABLE elastic_price.trained_model_new RENAME TO trained_model",

        "DROP INDEX IF EXISTS trained_model_model_name_idx",
        "DROP INDEX IF EXISTS trained_model_active_idx",

        "CREATE INDEX trained_model_model_name_idx ON elastic_price.trained_model (model_name)",
        "CREATE INDEX trained_model_active_idx ON elastic_price.trained_model (active)",

        
        # Comments for documentation
        "COMMENT ON SCHEMA elastic_price IS 'Schema for elastic price optimization functionality';",
        "COMMENT ON TABLE elastic_price.trained_model IS 'Stores metadata about trained elastic price models';"
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:
    return True
