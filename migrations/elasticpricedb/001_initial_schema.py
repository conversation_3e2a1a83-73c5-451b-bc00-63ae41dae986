#!/usr/bin/env python3


from facades.elasticpricedb.db import ElasticPriceDbFacade
from  .. migration import execute_pg_migration

# python3 -m cli.migration migrate --migration=migrations/elasticpricedb/001_initial_schema --action=up --dry-run

def up(dry_run=False)->bool:
        
    sql_statements = [
        # Enable UUID extension for generating unique identifiers
        "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
        
        # Create schema if it doesn't exist
        "CREATE SCHEMA IF NOT EXISTS elastic_price;",
        
        # Table for storing model training results

        "DROP TABLE IF EXISTS elastic_price.trained_model",
        """
        CREATE TABLE elastic_price.trained_model (
            id serial8 PRIMARY KEY,
            active BOOLEAN DEFAULT TRUE,
            loss NUMERIC(10, 6),
            accuracy NUMERIC(10, 6),
            model_dump BYTEA,
            training_data_size INTEGER,
            validation_data_size INTEGER,
            start_time TIMESTAMP WITH TIME ZONE,
            end_time TIMESTAMP WITH TIME ZONE, 
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,

        "CREATE INDEX trained_model_active_idx ON elastic_price.trained_model (active)",

        
        # Comments for documentation
        "COMMENT ON SCHEMA elastic_price IS 'Schema for elastic price optimization functionality';",
        "COMMENT ON TABLE elastic_price.trained_model IS 'Stores metadata about trained elastic price models';"
    ]

    return execute_pg_migration(ElasticPriceDbFacade.instance(), sql_statements, dry_run)


def down(dry_run=False)->bool:    
    print("Migration DOWN not implemented yet")
    return True
