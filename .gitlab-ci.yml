---
stages:
  - test
  - build
  - deploy

variables:
  SERVICE_NAME: "elastic-price"
  HELM_PROJECT_NAME: "helm-${SERVICE_NAME}"
  BUILD_NUMBER: "v${CI_PIPELINE_ID}"

.kaniko_auth: &kaniko_auth
  before_script:
    - mkdir -p /kaniko/.docker
    - mkdir -p /kaniko/.docker/certs
    - cat $CA_CERT >> /kaniko/.docker/certs/ca-certificates.crt
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json

.ssh_key: &ssh_key
  before_script:
    - apt update
    - apt install -y git wget curl
    - wget https://github.com/mikefarah/yq/releases/download/v4.40.4/yq_linux_amd64.tar.gz -O - | tar xz && mv yq_linux_amd64 /usr/bin/yq
    - yq --version
    - "command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )"
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - echo -e "Host gitlab.sl.local\n\tPort 2202\n\tStrictHostKeyChecking no" >> ~/.ssh/config
    - chmod 600 ~/.ssh/id_rsa
    - curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/latest/download/argocd-linux-amd64
    - chmod +x /usr/local/bin/argocd

## Lint
DockerLint:
  stage: test
  image: harbor.sl.local/proxy-cache/hadolint/hadolint:latest-alpine
  script:
    - hadolint --failure-threshold error ./docker/Dockerfile
  only:
    changes:
      - ./docker/Dockerfile

## Build
DockerBuild:
  stage: build
  environment:
    name: $ENVIRONMENT
  image:
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  <<: *kaniko_auth
  script:
    - cat ${ENV} > .env
    - /kaniko/executor
      --context .
      --dockerfile ./docker/Dockerfile
      --destination "${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/$SERVICE_NAME:v${CI_PIPELINE_ID}"
      --destination "${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/$SERVICE_NAME:latest"
      --cache=true
      --cache-ttl=24h
      --registry-certificate harbor.sl.local=/kaniko/.docker/certs/ca-certificates.crt
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      variables:
        ENV_FILE: .env
        ENVIRONMENT: $CI_COMMIT_REF_NAME
      when: always
    - if: '$CI_COMMIT_BRANCH == "master"'
      variables:
        ENV_FILE: .env
        ENVIRONMENT: prod
      when: always
    - if: '$CI_COMMIT_BRANCH == "stage"'
      variables:
        ENV_FILE: .env
        ENVIRONMENT: stage
      when: always
    - if: '$CI_COMMIT_BRANCH == "rc"'
      variables:
        ENV_FILE: .env
        ENVIRONMENT: rc
      when: always

## Deploy CLI
DeployCLI:
  stage: deploy
  environment:
    name: $ENVIRONMENT
  needs:
    - job: DockerBuild
      optional: false
  script:
    - "command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )"
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_DEPLOY_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host *\n\tStrictHostKeyChecking no" >> ~/.ssh/config
    - |
      export IMGTAG=${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/$SERVICE_NAME:v${CI_PIPELINE_ID}
      sed -i "s#{IMGTAG}#$IMGTAG#g" docker-compose.ci.common.yml
    - |
      DEPLOY_SERVER_IPS=$(echo $DEPLOY_SERVER_IP | tr "," "\n")
      for SERVER_IP in $DEPLOY_SERVER_IPS
      do
        echo "Deploying to $SERVER_IP"
        scp -i ~/.ssh/id_rsa -P2202 -o StrictHostKeyChecking=no ./docker-compose.ci.common.yml admin@$SERVER_IP:/deploy/docker-compose.yml
        ssh -i ~/.ssh/id_rsa -p2202 admin@$SERVER_IP "cd /deploy && docker compose up -d"
        ssh -i ~/.ssh/id_rsa -p2202 admin@$SERVER_IP "docker image prune -fa"
      done
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      variables:
        ENVIRONMENT: $CI_COMMIT_REF_NAME
      when: always
    - if: '$CI_COMMIT_BRANCH == "master"'
      variables:
        ENVIRONMENT: prod
      when: always
    - if: '$CI_COMMIT_BRANCH == "stage"'
      variables:
        ENVIRONMENT: stage
      when: always
    - if: '$CI_COMMIT_BRANCH == "rc"'
      variables:
        ENVIRONMENT: rc
      when: always

## Deploy API
DeployAPI:
  stage: deploy
  environment:
    name: $ENVIRONMENT
  needs:
    - job: DockerBuild
      optional: false
  <<: *ssh_key
  script:
    - |
        echo "env:" > .env.tmp;
        while IFS= read -r var
        do
          if [ "$var" ]
          then
            key="$(cut -d '=' -f1 <<< "$var")"
            value="$(cut -d '=' -f2 <<< "$var")"
            if [ "$key" ] && [ "$value" ];
            then
              echo "  $key: $value"
            fi
          fi
        done < $ENV >> .env.tmp
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Deployer"
    - <NAME_EMAIL>:deploy/${HELM_PROJECT_NAME}.git ${HELM_PROJECT_NAME}
    - cd ${HELM_PROJECT_NAME}
    - sed -i "s/tag:\ \"[a-zA-Z0-9]\\+\"/tag:\ \"$BUILD_NUMBER\"/g" values/$ENVIRONMENT.yaml
    - mkdir -p chart/$ENVIRONMENT/templates
    - |
        echo "apiVersion: v1" > chart/$ENVIRONMENT/templates/secrets.yaml
        echo "kind: Secret" >> chart/$ENVIRONMENT/templates/secrets.yaml
        echo "metadata:" >> chart/$ENVIRONMENT/templates/secrets.yaml
        echo "  name: \"$SERVICE_NAME\"" >> chart/$ENVIRONMENT/templates/secrets.yaml
        echo "type: Opaque" >> chart/$ENVIRONMENT/templates/secrets.yaml
        echo "data:" >> chart/$ENVIRONMENT/templates/secrets.yaml
        while IFS= read -r var
        do
          if [ "$var" ]
          then
            key="$(cut -d '=' -f1 <<< "$var")"
            value="$(cut -d '=' -f2 <<< "$var")"
            if [ "$key" ] && [ "$value" ];
            then
              encoded_value=$(echo -n "$value" | base64 -w 0)
              echo "  $key: $encoded_value" >> chart/$ENVIRONMENT/templates/secrets.yaml
            fi
          fi
        done < $ENV
    - git add .
    - cat values/$ENVIRONMENT.yaml
    - git commit -am "[Deployer] Update Application to version $BUILD_NUMBER in $ENVIRONMENT environment"
    - git push --force
    - argocd login --insecure --username $ARGOCD_USER --password $ARGOCD_PASSWORD --grpc-web $ARGOCD_HOST
    - argocd app sync $ENVIRONMENT-$SERVICE_NAME --retry-backoff-duration=180s --retry-backoff-max-duration=6m --retry-limit=3 --loglevel=debug
    - argocd app wait $ENVIRONMENT-$SERVICE_NAME --timeout 6000
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      variables:
        ENVIRONMENT: "dev"
      when: always
    - if: '$CI_COMMIT_BRANCH == "rc"'
      variables:
        ENVIRONMENT: "rc"
      when: always
    - if: $CI_COMMIT_BRANCH == "stage"
      variables:
        ENVIRONMENT: "stage"
      when: always
    - if: '$CI_COMMIT_BRANCH == "master"'
      variables:
        ENVIRONMENT: "prod"
      when: always
