"""Logging configuration for the application."""
import logging
import os
import sys
from logging.handlers import RotatingFileHandler

def setup_logging(log_level=None, log_file_name:str = "app.log", log_to_file:bool = False, log_to_console:bool = True):
    """
    Configure logging for the application.
    
    Args:
        log_level: Optional log level to override the default or environment setting
        log_file_name: Name of the log file (default: "app.log")
        log_to_file: Whether to log to a file (default: False)
        log_to_console: Whether to log to console/stdout (default: True)
    """
    # Get log level from environment or use default
    if log_level is None:
        log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    
    # Convert string log level to logging constant
    numeric_level = getattr(logging, log_level, None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers to avoid duplicate logs
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Add console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # Add file handler if requested
    if log_to_file:
        log_dir = os.environ.get('LOG_DIR', 'logs')
        if not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir)
            except Exception as e:
                logging.warning(f"Could not create log directory {log_dir}: {e}")
        
        if os.path.exists(log_dir):
            log_file = os.path.join(log_dir, log_file_name)
            file_handler = RotatingFileHandler(
                log_file, 
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(numeric_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
    
    # Log startup message
    logging.info(f"Logging configured with level {log_level}" + 
                 (f", writing to {log_file_name}" if log_to_file else "") +
                 (", writing to console" if log_to_console else ""))
