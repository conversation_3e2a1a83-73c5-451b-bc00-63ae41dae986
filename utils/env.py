#!/usr/bin/env python3
"""Utility for loading environment variables from .env files."""
import os
import sys
import logging
from pathlib import Path
from typing import Dict, Optional
from dotenv import load_dotenv, dotenv_values, find_dotenv

logger = logging.getLogger(__name__)

def load_env_file(env_file: str = ".env") -> Dict[str, str]:
    """
    Load environment variables from a .env file.
    
    Args:
        env_file: Path to the .env file
        
    Returns:
        Dictionary of environment variables
    """
    env_path = Path(env_file)
    
    if not env_path.exists():
        logger.warning(f"Environment file {env_file} not found")
        return {}
    
    logger.info(f"Loading environment from {env_file}")
    
    # Use dotenv_values to get variables as a dict without setting them
    return dotenv_values(env_file)

def export_env_to_os(env_file: str = ".env") -> None:
    """
    Load environment variables from a .env file and export them to os.environ.
    
    Args:
        env_file: Path to the .env file
    """
    env_path = Path(env_file)
    
    if not env_path.exists():
        logger.warning(f"Environment file {env_file} not found")
        return
    
    # Load the .env file into os.environ
    load_dotenv(env_file, override=True)
    
    # Count how many variables were loaded for logging
    env_vars = dotenv_values(env_file)
    logger.info(f"Exported {len(env_vars)} environment variables from {env_file}")

def get_env_value(key: str, default: Optional[str] = None) -> Optional[str]:
    """
    Get an environment variable value, with fallback to .env file if not in os.environ.
    
    Args:
        key: Environment variable name
        default: Default value if not found
        
    Returns:
        Environment variable value or default
    """
    # First check if it's already in the environment
    value = os.environ.get(key)
    if value is not None:
        return value
    
    # Then check if we can find it in a .env file
    dotenv_path = find_dotenv(usecwd=True)
    if dotenv_path:
        # Load .env file without modifying the environment
        env_vars = dotenv_values(dotenv_path)
        if key in env_vars:
            return env_vars[key]
    
    # Return default if not found
    return default

def print_env_vars(env_file: str = ".env") -> None:
    """
    Print all environment variables from a .env file.
    
    Args:
        env_file: Path to the .env file
    """
    env_vars = load_env_file(env_file)
    
    if not env_vars:
        print(f"No environment variables found in {env_file}")
        return
    
    print(f"Environment variables in {env_file}:")
    for key, value in env_vars.items():
        # Mask sensitive values
        if any(sensitive in key.lower() for sensitive in ["password", "secret", "key", "token"]):
            masked_value = value[:3] + "*" * (len(value) - 3) if len(value) > 3 else "***"
            print(f"{key}={masked_value}")
        else:
            print(f"{key}={value}")

if __name__ == "__main__":
    # Simple CLI for env file operations
    import argparse
    
    parser = argparse.ArgumentParser(description="Environment file utilities")
    parser.add_argument("--file", "-f", default=".env", help="Path to .env file")
    parser.add_argument("--export", "-e", action="store_true", help="Export variables to environment")
    parser.add_argument("--print", "-p", action="store_true", help="Print variables from .env file")
    
    args = parser.parse_args()
    
    if args.export:
        export_env_to_os(args.file)
        print(f"Exported environment variables from {args.file}")
    
    if args.print:
        print_env_vars(args.file)
    
    if not (args.export or args.print):
        parser.print_help()
