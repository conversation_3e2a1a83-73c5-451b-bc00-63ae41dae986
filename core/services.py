# """Core services for the application."""
# import logging
# from typing import List, Optional, Dict, Any

# from app.core.models import User
# from app.db.repositories.user_repository import UserRepository

# # Configure logging
# # logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)

# class UserService:
#     """Service for user-related operations."""
    
#     def __init__(self):
#         """Initialize the user service with a repository."""
#         self.repository = UserRepository()
    
#     def get_users(self, skip: int = 0, limit: int = 100, active_only: bool = False) -> List[User]:
#         """
#         Get a list of users with pagination.
        
#         Args:
#             skip: Number of users to skip
#             limit: Maximum number of users to return
#             active_only: If True, only return active users
            
#         Returns:
#             List of users
#         """
#         logger.info(f"Getting users (skip={skip}, limit={limit}, active_only={active_only})")
#         if active_only:
#             return self.repository.find_active_users()
#         return self.repository.find_all()
    
#     def get_user(self, user_id: int) -> Optional[User]:
#         """
#         Get a user by ID.
        
#         Args:
#             user_id: ID of the user to retrieve
            
#         Returns:
#             User or None if not found
#         """
#         logger.info(f"Getting user with ID {user_id}")
#         return self.repository.find_by_id(user_id)
    
#     def get_user_by_username(self, username: str) -> Optional[User]:
#         """
#         Get a user by username.
        
#         Args:
#             username: Username to search for
            
#         Returns:
#             User or None if not found
#         """
#         logger.info(f"Getting user with username '{username}'")
#         return self.repository.find_by_username(username)
    
#     def create_user(self, user_data: Dict[str, Any]) -> User:
#         """
#         Create a new user.
        
#         Args:
#             user_data: User data dictionary
            
#         Returns:
#             Created user
#         """
#         logger.info(f"Creating new user with username '{user_data.username}'")
#         user = User(
#             username=user_data.username,
#             email=user_data.email
#         )
#         return self.repository.create(user)
    
#     def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Optional[User]:
#         """
#         Update an existing user.
        
#         Args:
#             user_id: ID of the user to update
#             user_data: User data dictionary with fields to update
            
#         Returns:
#             Updated user or None if not found
#         """
#         logger.info(f"Updating user with ID {user_id}")
#         user = self.repository.find_by_id(user_id)
#         if not user:
#             return None
            
#         # Update user fields if provided
#         if hasattr(user_data, 'email') and user_data.email:
#             user.email = user_data.email
            
#         return self.repository.update(user)
    
#     def delete_user(self, user_id: int) -> bool:
#         """
#         Delete a user.
        
#         Args:
#             user_id: ID of the user to delete
            
#         Returns:
#             True if deleted, False if not found
#         """
#         logger.info(f"Deleting user with ID {user_id}")
#         return self.repository.delete(user_id)


# # Service factory functions
# _user_service = None

# def get_user_service() -> UserService:
#     """
#     Get or create a UserService instance.
    
#     Returns:
#         UserService instance
#     """
#     global _user_service
#     if _user_service is None:
#         _user_service = UserService()
#     return _user_service