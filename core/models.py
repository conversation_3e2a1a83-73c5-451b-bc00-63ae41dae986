# """Core data models for the application."""
# from dataclasses import dataclass
# from datetime import datetime
# from typing import Optional, List


# @dataclass
# class User:
#     """User model representing application users."""
#     id: Optional[int] = None
#     username: str = ""
#     email: str = ""
#     created_at: datetime = None
#     is_active: bool = True

#     def __post_init__(self):
#         if self.created_at is None:
#             self.created_at = datetime.now()


# @dataclass
# class Item:
#     """Item model representing data items in the application."""
#     id: Optional[int] = None
#     name: str = ""
#     description: str = ""
#     owner_id: int = None
#     created_at: datetime = None
    
#     def __post_init__(self):
#         if self.created_at is None:
#             self.created_at = datetime.now()