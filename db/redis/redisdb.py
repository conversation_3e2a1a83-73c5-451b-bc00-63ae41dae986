#!/usr/bin/env python3

import logging
from typing import Dict, Optional
import redis

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RedisClient:
    
    def __init__(self):
        """Initialize the Redis cache facade."""
        self._redis_clients: Dict[str, Optional[redis.Redis]] = {}
        # logger.info("RedisClient initialized")
        
    def __del__(self):
        """Clean up connections when object is destroyed."""
        self.close_all_connections()

    
    def client(self, db: int, host: str, port: int, password: str, decode_responses: bool=True, **kwargs) -> Optional[redis.Redis]:
        """
        Get or create the Redis client connection.
        
        Returns:
            Redis client instance
        """
        conn_key = f"{host}:{port}/{db}"

        if conn_key not in self._redis_clients or self._redis_clients[conn_key] is None :
            try:
                config = {
                    'db': db,
                    'host': host,
                    'port': port,
                    'password': password,
                    'decode_responses': decode_responses
                }

                config.update(kwargs)

                self._redis_clients[conn_key] = redis.Redis(**config)
                # Test connection
                self._redis_clients[conn_key].ping()
                # logger.info("Connected to Redis server")
                
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                self._redis_clients[conn_key] = None
        
        return self._redis_clients[conn_key]
    

    def _close_connection(self, conn_key: str) -> bool:
        """
        Close the Redis client connection.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Close the connection
            if self._redis_clients[conn_key] is not None:
                self._redis_clients[conn_key].close()
                
            del self._redis_clients[conn_key]

            # logger.info(f"Redis connection {conn_key} closed")
            return True
        except Exception as e:
            logger.error(f"Error closing Redis connection: {str(e)}")
            return False
        
    def close_all_connections(self):
        """Close all database connections."""
        for db in list(self._redis_clients.keys()):
            self._close_connection(db)
        # logger.info("All connections closed")

