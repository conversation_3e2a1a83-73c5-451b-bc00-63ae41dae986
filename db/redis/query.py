#!/usr/bin/env python3

import logging
from typing import Any, Dict, Optional, Callable
import redis

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Query:
    
    def __init__(self, connection: Optional[redis.Redis]):
        """Initialize the Redis cache facade."""
        self.connection  = connection
    
    def set(self, key: str, value: str|bytes, expiry: Optional[int] = None) -> bool:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to store
            expiry: Optional expiration time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            if expiry:
                return self.connection.setex(key, expiry, value)
            else:
                return self.connection.set(key, value)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False
    #def
    
    def get(self, key: str, default: str|bytes = None) -> Optional[str|bytes]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            default: Default value if key doesn't exist
            
        Returns:
            Value or None if not found
        """
        if not self.connection:
            return None
        
        try:
            value = self.connection.get(key)
            if value is None:
                return default
            return value
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
            return default
    #def

    def delete(self, key: str) -> bool:
        """
        Delete a key from the cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.delete(key))
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {str(e)}")
            return False
    #def
    
    def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.exists(key))
        except Exception as e:
            logger.error(f"Error checking existence of key {key}: {str(e)}")
            return False
    #def

    def expire(self, key: str, seconds: int) -> bool:
        """
        Set expiration time for a key.
        
        Args:
            key: Cache key
            seconds: Expiration time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.expire(key, seconds))
        except Exception as e:
            logger.error(f"Error setting expiration for key {key}: {str(e)}")
            return False
    #def
    
    def clear(self) -> bool:
        """
        Clear all keys in the current database.
        
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.flushdb())
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            return False
    #def
    
    def hash_set(self, name: str, key: str, value: str|bytes) -> bool:
        """
        Set a field in a hash.
        
        Args:
            name: Hash name
            key: Field name
            value: Field value
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.hset(name, key, value))
        except Exception as e:
            logger.error(f"Error setting hash field {name}:{key}: {str(e)}")
            return False
    #def

    
    def hash_get(self, name: str, key: str, default: str|bytes = None) -> Optional[str|bytes]:
        """
        Get a field from a hash.
        
        Args:
            name: Hash name
            key: Field name
            default: Default value if field doesn't exist
            
        Returns:
            Value or default if not found
        """
        if not self.connection:
            return default
        
        try:
            value = self.connection.hget(name, key)
            if value is None:
                return default
            return value
        except Exception as e:
            logger.error(f"Error getting hash field {name}:{key}: {str(e)}")
            return default
    #def
    
    def hash_delete(self, name: str, key: str) -> bool:
        """
        Delete a field from a hash.
        
        Args:
            name: Hash name
            key: Field name
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            return False
        
        try:
            return bool(self.connection.hdel(name, key))
        except Exception as e:
            logger.error(f"Error deleting hash field {name}:{key}: {str(e)}")
            return False
    #def
    
    def hash_get_all(self, name: str) -> Dict[str, str|bytes]:
        """
        Get all fields and values from a hash.
        
        Args:
            name: Hash name
            
        Returns:
            Dictionary of field-value pairs
        """
        if not self.connection:
            return {}
        
        try:
            result = self.connection.hgetall(name)
            return result
        except Exception as e:
            logger.error(f"Error getting all hash fields for {name}: {str(e)}")
            return {}
    #def

    def get_or_set(self, key: str, callback: Callable[[], str|bytes], expiry: Optional[int] = None) -> str|bytes:
        """
        Get a value from the cache or set it if it doesn't exist.
        user_data = cache.get_or_set(
            key=f"user:{user_id}",
            callback=lambda: fetch_user_from_database(user_id),
            expiry=3600  # Cache for 1 hour
        )
        
        Args:
            key: Cache key
            callback: Function to call to generate the value if not in cache
            expiry: Optional expiration time in seconds
        
        Returns:
            Cached value (either existing or newly generated)
        """
        if not self.connection:
            # If Redis is unavailable, just execute the callback
            return callback()
        
        try:
            # Try to get from cache first
            value = self.connection.get(key)
            if value is not None:
                logger.debug(f"Cache hit for key: {key}")
                return value
            
            # Cache miss - generate value using callback
            logger.debug(f"Cache miss for key: {key}")
            result = callback()
            
            # Store in cache
            if expiry:
                self.connection.setex(key, expiry, result)
            else:
                self.connection.set(key, result)
            
            return result
        except Exception as e:
            logger.error(f"Error in get_or_set for key {key}: {str(e)}")
            # Fall back to callback if anything goes wrong
            return callback()
    #def
#class

    