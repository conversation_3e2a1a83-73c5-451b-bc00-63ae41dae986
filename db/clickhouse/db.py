#!/usr/bin/env python3

import time
import logging
from typing import Dict, Any
import clickhouse_connect
from lib.singleton import SingletonDoubleChecked

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ClickHouseDbClient(SingletonDoubleChecked):
    """
    Facade for managing ClickHouse database connections.
    Implements connection pooling, error handling, and automatic reconnection.
    """

    def __init__(self, max_db_connections:int = 20, connection_timeout:int = 3600, client_name='app'):
        self.max_db_connections = max_db_connections
        self.connection_timeout = connection_timeout
        self.client_name = client_name
        
        """Initialize the connection pool."""
        self.__connections: Dict[str, Dict] = {}  # {conn_key: {client, last_used}}
        # logger.info("ClickHouseDbClient initialized")

    def __del__(self):
        """Clean up connections when object is destroyed."""
        self.close_all_connections()

    def get_connection(self, host: str, port: int, user: str, password: str, dbname: str, **kwargs) -> Any:

        conn_key = f"{host}:{port}/{dbname}"

        # Check if we have a valid connection
        if self._is_valid_connection(conn_key):
            # Update last used time
            self.__connections[conn_key]['last_used'] = time.time()
            return self.__connections[conn_key]['client']
        
        # Create a new connection
        try:
            # logger.info(f"Creating new connection to ClickHouse on {conn_key}")

            config = {
                "host":host,
                "port":port,
                "username":user,
                "password":password,
                "database":dbname,
                "send_receive_timeout":30,
                "client_name":self.client_name,
                "compress":True,
            }

            config.update(kwargs)

            client = clickhouse_connect.get_client(**config)            

            # client = clickhouse_connect.get_client(
            #     host=host,
            #     port=port,
            #     username=user,
            #     password=password,
            #     database=dbname,
            #     send_receive_timeout=30,
            #     client_name=self.client_name,
            #     compress=True,
            #     # settings={
            #     #     'cluster': CLICKHOUSE_CLUSTER_NAME,
            #     #     'max_execution_time': 60,
            #     #     'max_block_size': 100000,
            #     #     'connect_timeout_with_failover_ms': 5000,
            #     #     'keep_alive_timeout': 10
            #     # },
            #     **kwargs
            # )
            
            # Store connection with timestamp
            self.__connections[conn_key] = {
                'client': client,
                'last_used': time.time()
            }
            
            return client
            
        except Exception as e:
            raise ConnectionError(f"Could not connect to any ClickHouse node for customer {conn_key}")

    def _is_valid_connection(self, conn_key: str) -> bool:
        """
        Check if a connection exists, is not expired, and is still valid.
        
        Args:
            conn_key: Connection key to check
            
        Returns:
            True if connection is valid, False otherwise
        """
        if conn_key not in self.__connections:
            return False
            
        conn_info = self.__connections[conn_key]
        
        # Check if connection is expired
        if time.time() - conn_info['last_used'] > self.connection_timeout:
            logger.info(f"Connection to {conn_key} expired")
            self._close_connection(conn_key)
            return False
            
        # Check if connection is still alive
        try:
            # Ping the server with a simple query
            conn_info['client'].query("SELECT 1")
            return True
        except Exception:
            logger.info(f"Connection to {conn_key} is no longer valid")
            self._close_connection(conn_key)
            return False

    def _close_connection(self, conn_key: str):
        """
        Close a specific database connection.
        
        Args:
            conn_key: Host to close connection to
        """
        if conn_key in self.__connections:
            # try:
            #     # ClickHouse client doesn't have an explicit close method,
            #     # but we can remove it from our pool
            #     logger.info(f"Removed connection to {conn_key}")
            # except Exception as e:
            #     logger.warning(f"Error closing connection to {conn_key}: {str(e)}")
            
            del self.__connections[conn_key]

    def close_all_connections(self):
        """Close all database connections."""
        for host in list(self.__connections.keys()):
            self._close_connection(host)
        # logger.info("All connections closed")

    def close_connection(self):
        """Close all connections (for backward compatibility)."""
        self.close_all_connections()

