#!/usr/bin/env python3

import logging
from typing import Dict, Generator, Optional, Any, List, Union
import pandas as pd

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Query:

    def __init__(self, connection:Optional[Any]):
        self._connection = connection
        self.connection = connection
        # logger.info("Query initialized")
    #def

    def query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Generator[Dict[str, Any], None, None]]:
        """
        Execute a query and return results as a list of dictionaries.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            Query results as a list of dictionaries
        """
        try:
            return self._connection.query(query, parameters=params or {})
        except Exception as e:
            logger.error(f"Query execution error for query {query}: {str(e)}")
            return None
    #def

    def query_all(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        result = self.query(query, params)
        if result is None:
            return []
        
        return list(result.named_results())
    #def
    
    def query_one(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        results = self.query_all(query, params)
        return results[0] if results else None
    #def

    def query_df(self, query: str, params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Execute a query and return results as a pandas DataFrame.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            Query results as a pandas DataFrame
        """
        try:
            result = self._connection.query_df(query, parameters=params or {})
            return result
        except Exception as e:
            logger.error(f"DataFrame query execution error for query {query}: {str(e)}")
            return pd.DataFrame()
    #def
    
    def execute_command(self, command: str) -> bool:
        """
        Execute a command that doesn't return results (INSERT, UPDATE, etc.).
        
        Args:
            command: SQL command to execute
        """
        try:
            self._connection.command(command)
            logger.info(f"Command executed successfully for command {command}")
        except Exception as e:
            logger.error(f"Command execution error for command {command}: {str(e)}")
            return False
        
        return True
    #def
    
    def batch_query(self, queries: List[str]) -> List[List[Dict[str, Any]]]:
        """
        Execute multiple queries in sequence.
        
        Args:
            queries: List of SQL queries to execute
            
        Returns:
            List of query results
        """
        results = []
        for query in queries:
            results.append(self.query_all(query))
        return results
    #def

    def execute_on_cluster(self, claster_name: str, query: str) -> List[Dict[str, Any]]:
        """
        Execute a query on the entire cluster.
        
        Args:
            query: SQL query to execute
        
        Returns:
            Query results
        """
        
        # Add cluster name to the query if not already present
        if "ON CLUSTER" not in query.upper():
            # Find the position after the first command (SELECT, CREATE, ALTER, DROP, INSERT, UPDATE, DELETE)
            import re
            match = re.search(r'^\s*(SELECT|CREATE|ALTER|DROP|INSERT|UPDATE|DELETE)\s+', query, re.IGNORECASE)
            if match:
                position = match.end()
                query = query[:position] + f"ON CLUSTER '{claster_name}' " + query[position:]
        
        return self._connection.query(query).result_rows
    #def
