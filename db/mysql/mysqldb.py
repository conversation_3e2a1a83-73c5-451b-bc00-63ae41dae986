#!/usr/bin/env python3

import time
import logging
import pymysql
from typing import Dict, Optional

# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MysqlDbClient:
    """
    Facade for managing database connections to the MySQL database system.
    Implements connection pooling, error handling, and automatic reconnection.
    """

    def __init__(self, max_db_connections:int = 20, connection_timeout:int = 3600):
        self.max_db_connections = max_db_connections
        self.connection_timeout = connection_timeout
        """Initialize the connection pool."""
        self.__connections: Dict[str, Dict] = {}  # {db_name: {connection, last_used}}
        self.__connection_count = 0
        # logger.info("MysqlDbClient initialized")

    def __del__(self):
        self.close_all_connections()

    
    def get_connection(self, dbname: str, host: str, port: int, user: str, password: str, **kwargs) -> pymysql.Connection:
        """
        Get or create a database connection.
        
        Args:
            dbname: Database name
            host: Database host
            port: Database port
            user: Database user
            password: Database password
            
        Returns:
            Database connection
            
        Raises:
            ConnectionError: If connection to the database fails
        """
        conn_key = f"{host}:{port}/{dbname}"

        # Fast path: Check if connection exists and update timestamp without validation
        if conn_key in self.__connections:
            conn_info = self.__connections[conn_key]
            # Only validate if connection is older than a threshold (e.g., 60 seconds)
            if time.time() - conn_info['last_used'] < 10:
                conn_info['last_used'] = time.time()
                return conn_info['connection']
            # Otherwise fall through to regular validation

        # Regular path: Validate connection or create new one
        if self._is_valid_connection(conn_key):
            self.__connections[conn_key]['last_used'] = time.time()
            return self.__connections[conn_key]['connection']

        # Clean up old connections if we're at the limit
        if self.__connection_count >= self.max_db_connections:
            self._cleanup_old_connections()
        
        # Create a new connection
        try:
            # logger.info(f"Creating new connection to {dbname} on {host}")

            config = {
                "host": host,
                "port": port,
                "user": user,
                "password": password,
                "database": dbname,
                "charset": 'utf8mb4',
                "cursorclass": pymysql.cursors.DictCursor,
                "connect_timeout": 60,
                "read_timeout": 60,
                "write_timeout": 60,
                # "autocommit": True,
            }

            config.update(kwargs)

            connection = pymysql.connect(**config)
            
            # Store connection with timestamp
            self.__connections[conn_key] = {
                'connection': connection,
                'last_used': time.time()
            }
            self.__connection_count += 1
            
            return connection
            
        except Exception as e:
            logger.error(f"Failed to connect to {dbname} on {host}: {str(e)}")
            raise ConnectionError(f"Failed to connect to MySQL database {dbname} on {host}:{port}: {str(e)}")

    def _is_valid_connection(self, conn_key: str) -> bool:
        """
        Check if a connection exists, is not expired, and is still valid.
        
        Args:
            dbname: Database name to check
            
        Returns:
            True if connection is valid, False otherwise
        """
        if conn_key not in self.__connections:
            return False
            
        conn_info = self.__connections[conn_key]
        
        # Check if connection is expired
        if time.time() - conn_info['last_used'] > self.connection_timeout:
            logger.info(f"Connection to {conn_key} expired")
            self._close_connection(conn_key)
            return False
            
        # Check if connection is still alive
        try:
            conn_info['connection'].ping(reconnect=True)
            return True
        except Exception:
            logger.info(f"Connection to {conn_key} is no longer valid")
            self._close_connection(conn_key)
            return False

    def _cleanup_old_connections(self):
        """Close the oldest connections to free up resources."""
        if not self.__connections:
            return
            
        # Sort connections by last used time
        connections = sorted(
            self.__connections.items(),
            key=lambda x: x[1]['last_used']
        )
        
        # Close the oldest connection
        oldest_db = connections[0][0]
        self._close_connection(oldest_db)

    def _close_connection(self, conn_key: str):
        """
        Close a specific database connection.
        
        Args:
            conn_key: Database name to close
        """
        if conn_key in self.__connections:
            try:
                self.__connections[conn_key]['connection'].close()
                # logger.info(f"Closed connection to {conn_key}")
            except Exception as e:
                logger.warning(f"Error closing connection to {conn_key}: {str(e)}")
            
            del self.__connections[conn_key]
            self.__connection_count -= 1

    def close_all_connections(self):
        """Close all database connections."""
        for conn_key in list(self.__connections.keys()):
            self._close_connection(conn_key)
        # logger.info("All connections closed")

