#!/usr/bin/env python3

import logging
from typing import Dict, Optional, List, Any, Union, Tuple
import pandas as pd
import pymysql


# Configure logging
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Query:
    """
    Facade for executing queries against the MySQL databases.
    Handles query execution, error handling, and result formatting.
    """

    def __init__(self, connection:Optional[pymysql.Connection]):
        """Initialize the query facade."""
        self._connection = connection
        # logger.info("Query initialized")

    def query_all(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> List[Dict[str, Any]]:
        """
        Execute a query on the database.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Query results as a list of dictionaries
        """
        if not self._connection:
            logger.error("Cannot execute query: No connection to database")
            return []
            
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(query, params or ())
                result = cursor.fetchall()
                return result
        except Exception as e:
            logger.error(f"Query execution error on database: {str(e)}")
            raise
    #def
    
    def query_one(self, query: str, params: Optional[Union[Tuple, Dict, List]] = None) -> Optional[Dict[str, Any]]:
        """
        Execute a query on the database and return the first result.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple, dict, or list)
            
        Returns:
            First query result as a dictionary or None if no results
        """
        results = self.query_all(query, params)

        return results[0] if results else None
    #def
    

    def query_df(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> pd.DataFrame:
        """
        Execute a query on the database and return results as a pandas DataFrame.
        
        Args:
            query: SQL query to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Query results as a pandas DataFrame
        """
        results = self.query_all(query, params)
        return pd.DataFrame(results)

    
    def execute(self, query: str, params: Optional[Union[Tuple, Dict]] = None) -> int:
        """
        Execute a command on the database that doesn't return results (INSERT, UPDATE, etc.).
        
        Args:
            query: SQL command to execute
            params: Query parameters (tuple or dict)
            
        Returns:
            Number of affected rows
        """
        if not self._connection:
            logger.error("Cannot execute command: No connection to database")
            return 0
            
        try:
            with self._connection.cursor() as cursor:
                affected_rows = cursor.execute(query, params or ())
                self._connection.commit()
                logger.info(f"Command executed successfully on database. Affected rows: {affected_rows}")
                return affected_rows
        except Exception as e:
            logger.error(f"Command execution error on database: {str(e)}")
            self._connection.rollback()
            raise

    

    def batch_execute(self, queries: List[str]) -> List[int]:
        """
        Execute multiple commands in sequence on a client database.
        
        Args:
            customer_id: Customer ID to connect to
            queries: List of SQL commands to execute
            
        Returns:
            List of affected row counts
        """
        results = []
        if not self._connection:
            logger.error("Cannot execute command: No connection to database")
            return results
            
        try:
            with self._connection.cursor() as cursor:
                for query in queries:
                    affected_rows = cursor.execute(query)
                    results.append(affected_rows)
                self._connection.commit()
                logger.info(f"Batch executed successfully")
        except Exception as e:
            logger.error(f"Batch execution error : {str(e)}")
            self._connection.rollback()
            raise
            
        return results
