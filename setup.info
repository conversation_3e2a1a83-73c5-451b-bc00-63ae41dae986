1) create a virtual environment
python3 -m venv .venv 
or
python3 -m venv .venv --system-site-packages

echo "*" > .venv/.gitignore

2) activate
source .venv/bin/activate

which python3
python3 -m pip install --upgrade pip

pip3 install -r requirements.txt
pip3 install -r requirements.txt --no-index --find-links

pip3 install -U PPPPPPPPPP
pip3 freeze > requirements.txt

To deactivate run :   deactivate


4) jupyterlab
pip3 install jupyterlab
jupyter lab


5) for jupyterlab desktop 
pip3 install ipykernel
pip3 install ipykernel -U --force-reinstall



python3 -m ipykernel install --user --name=venv